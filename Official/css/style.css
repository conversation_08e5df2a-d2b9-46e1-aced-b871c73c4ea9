/* ========================================
   CSS Reset 和基础样式
   IE11 兼容性版本
   ======================================== */

/*
 * IE11 兼容性说明：
 * 1. 使用了 IE11 支持的 Flexbox 语法（-ms-flexbox）
 * 2. Grid 布局提供了 Flexbox fallback
 * 3. backdrop-filter 使用了更深的背景色作为 fallback
 * 4. CSS 渐变文字效果在 IE11 中使用纯色文字
 * 5. object-fit 使用背景图片方式实现
 * 6. 所有现代 CSS 特性都有相应的 IE11 兼容方案
 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background-color: #f9fafb;
    min-height: 1024px;
}

/* 链接样式 */
a {
    text-decoration: none;
    color: #212936;
}

a:hover {
    text-decoration: none;
    color: #2c7dff;
}

/* 页面容器样式 */
[pages] {
    min-height: 50vh;
    display: none;
}

iframe {
    width: 100%;
    height: 100vh;
    border: none;
}

/* ========================================
   布局容器
   ======================================== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* ========================================
   颜色系统 - 文字颜色
   ======================================== */
.text-primary {
    color: #2563eb;
}

.text-secondary {
    color: #1e40af;
}

.text-gray-900 {
    color: #111827;
}

.text-gray-800 {
    color: #1f2937;
}

.text-gray-700 {
    color: #374151;
}

.text-gray-600 {
    color: #4b5563;
}

.text-gray-500 {
    color: #6b7280;
}

.text-gray-400 {
    color: #9ca3af;
}

.text-white {
    color: #ffffff;
}

.text-green-500 {
    color: #10b981;
}

.text-green-600 {
    color: #059669;
}

.text-green-700 {
    color: #047857;
}

.text-red-500 {
    color: #ef4444;
}

.text-blue-100 {
    color: #dbeafe;
}

/* ========================================
   颜色系统 - 背景颜色
   ======================================== */
.bg-primary {
    background-color: #2563eb;
}

.bg-secondary {
    background-color: #1e40af;
}

.bg-white {
    background-color: #ffffff;
}

.bg-gray-50 {
    background-color: #f9fafb;
}

.bg-gray-100 {
    background-color: #f3f4f6;
}

.bg-blue-50 {
    background-color: #eff6ff;
}

.bg-blue-100 {
    background-color: #dbeafe;
}

.bg-green-50 {
    background-color: #ecfdf5;
}

.bg-green-100 {
    background-color: #dcfce7;
}
.bg-green-500 {
    background-color: #10b981;
}

/* ========================================
   字体系统
   ======================================== */
.font-logo {
    font-family: "Pacifico", serif;
}

.font-sans {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* 字体粗细 */
.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

/* 字体大小 */
.text-xs {
    font-size: 0.75rem;
}

.text-sm {
    font-size: 0.875rem;
}

.text-lg {
    font-size: 1.125rem;
}

.text-xl {
    font-size: 1.25rem;
}

.text-2xl {
    font-size: 1.5rem;
}

.text-3xl {
    font-size: 1.875rem;
}

.text-4xl {
    font-size: 2.25rem;
}

.text-5xl {
    font-size: 3rem;
}

/* 间距样式 */
.p-6 {
    padding: 1.5rem;
}

.p-8 {
    padding: 2rem;
}

.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
}

.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.py-2\.5 {
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
}

.py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
}

.m-0 {
    margin: 0;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mb-8 {
    margin-bottom: 2rem;
}

.mb-10 {
    margin-bottom: 2.5rem;
}

.mb-16 {
    margin-bottom: 4rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.mr-4 {
    margin-right: 1rem;
}

.ml-12 {
    margin-left: 3rem;
}

.mt-6 {
    margin-top: 1.5rem;
}

.mt-8 {
    margin-top: 2rem;
}

.mt-12 {
    margin-top: 3rem;
}

.mt-0\.5 {
    margin-top: 0.125rem;
}

/* 宽度和高度 */
.w-4 {
    width: 1rem;
}

.w-5 {
    width: 1.25rem;
}

.w-6 {
    width: 1.5rem;
}

.w-12 {
    width: 3rem;
}

.w-16 {
    width: 4rem;
}

.w-20 {
    width: 5rem;
}

.w-48 {
    width: 12rem;
}

.w-full {
    width: 100%;
}

.h-1 {
    height: 0.25rem;
}

.h-4 {
    height: 1rem;
}

.h-5 {
    height: 1.25rem;
}

.h-6 {
    height: 1.5rem;
}

.h-12 {
    height: 3rem;
}

.h-16 {
    height: 4rem;
}

.h-48 {
    height: 12rem;
}

.h-96 {
    height: 24rem;
}

.h-full {
    height: 100%;
}

.max-w-2xl {
    max-width: 42rem;
}

.max-w-400px {
    max-width: 400px;
}

/* 边框圆角 */
.rounded-lg {
    border-radius: 0.5rem;
}

.rounded-xl {
    border-radius: 0.75rem;
}

.rounded-full {
    border-radius: 9999px;
}

.rounded-button {
    border-radius: 4px;
}

/* 阴影 */
.shadow-md {
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-lg {
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 定位 */
.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.sticky {
    position: sticky;
}

.top-0 {
    top: 0;
}

.top-4 {
    top: 1rem;
}

.right-4 {
    right: 1rem;
}

.inset-0 {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

/* Z-index */
.z-10 {
    z-index: 10;
}

.z-50 {
    z-index: 50;
}

.z-1000 {
    z-index: 1000;
}

/* 显示 */
.hidden {
    display: none;
}

.block {
    display: block;
}

.inline-block {
    display: inline-block;
}

/* Flexbox */
.flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.flex-col {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
}

.items-center {
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.items-start {
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
}

.justify-center {
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.justify-between {
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
}

.flex-grow {
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
}

/* Grid - IE11 兼容版本 */
.grid {
    display: -ms-grid;
    display: grid;
}

.grid-cols-2 {
    -ms-grid-columns: 1fr 1fr;
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

/* IE11 Grid fallback - 使用 flexbox */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .grid {
        display: -ms-flexbox !important;
        display: flex !important;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    }

    .grid-cols-2 > * {
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
    }

    .grid-cols-3 > * {
        -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }

    .grid-cols-4 > * {
        -ms-flex: 0 0 25%;
        flex: 0 0 25%;
        max-width: 25%;
    }
}

.col-span-3 {
    -ms-grid-column-span: 3;
    grid-column: span 3 / span 3;
}

.gap-6 {
    gap: 1.5rem;
}

/* IE11 gap fallback */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .gap-6 > * {
        margin-right: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .gap-6 > *:last-child {
        margin-right: 0;
    }
}

.gap-8 {
    gap: 2rem;
}

/* IE11 gap fallback */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .gap-8 > * {
        margin-right: 2rem;
        margin-bottom: 2rem;
    }

    .gap-8 > *:last-child {
        margin-right: 0;
    }
}

/* 间距工具类 */
.space-x-4 > * + * {
    margin-left: 1rem;
}

.space-x-8 > * + * {
    margin-left: 2rem;
}

.space-y-3 > * + * {
    margin-top: 0.75rem;
}

/* 文本对齐 */
.text-center {
    text-align: center;
}

/* 溢出 */
.overflow-hidden {
    overflow: hidden;
}

.overflow-y-auto {
    overflow-y: auto;
}

/* 透明度 */
.opacity-20 {
    opacity: 0.2;
}

/* 对象适配 */
.object-cover {
    object-fit: cover;
}

.object-center {
    object-position: center;
}

/* IE11 object-fit fallback */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .object-cover {
        width: 100%;
        height: 100%;
    }

    /* 对于图片，使用背景图片方式实现 cover 效果 */
    .object-cover img {
        display: none;
    }

    .object-cover {
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
    }
}

/* 背景 */
.bg-cover {
    background-size: cover;
}

.bg-center {
    background-position: center;
}

/* 过渡效果 */
.transition {
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
}

.transition-transform {
    -webkit-transition: -webkit-transform 0.3s ease;
    transition: transform 0.3s ease;
}

.duration-300 {
    -webkit-transition-duration: 0.3s;
    transition-duration: 0.3s;
}

/* 悬停效果 */
.hover\:text-primary:hover {
    color: #2563eb;
}

.hover\:bg-secondary:hover {
    background-color: #1e40af;
}

.hover\:bg-blue-50:hover {
    background-color: #eff6ff;
}

.hover\:shadow-lg:hover {
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.hover\:shadow-md:hover {
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.hover\:-translate-y-1:hover {
    -webkit-transform: translateY(-0.25rem);
    transform: translateY(-0.25rem);
}

/* 边框 */
.border {
    border: 1px solid #e5e7eb;
}

.border-2 {
    border: 2px solid;
}

.border-t {
    border-top: 1px solid #e5e7eb;
}

.border-primary {
    border-color: #2563eb;
}

.border-gray-100 {
    border-color: #f3f4f6;
}

/* 按钮样式 */
button {
    border: none;
    outline: none;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    text-align: center;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #2563eb;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #1e40af;
}

/* 特殊高度 */
.h-600px {
    height: 600px;
}

.min-h-1024px {
    min-height: 1024px;
}

/* 渐变背景 */
.bg-gradient-to-r {
    background: linear-gradient(to right, #eff6ff, #dbeafe);
}

/* 响应式设计 */
@media (min-width: 768px) {
    .md\:flex {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
    }

    .md\:flex-row {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -webkit-flex-direction: row;
        -ms-flex-direction: row;
        flex-direction: row;
    }

    .md\:grid-cols-2 {
        -ms-grid-columns: 1fr 1fr;
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\:grid-cols-3 {
        -ms-grid-columns: 1fr 1fr 1fr;
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .md\:grid-cols-4 {
        -ms-grid-columns: 1fr 1fr 1fr 1fr;
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    .md\:col-span-3 {
        -ms-grid-column-span: 3;
        grid-column: span 3 / span 3;
    }

    /* IE11 响应式 Grid fallback */
    @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
        .md\:grid-cols-2 > * {
            -ms-flex: 0 0 50%;
            flex: 0 0 50%;
            max-width: 50%;
        }

        .md\:grid-cols-3 > * {
            -ms-flex: 0 0 33.333333%;
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
        }

        .md\:grid-cols-4 > * {
            -ms-flex: 0 0 25%;
            flex: 0 0 25%;
            max-width: 25%;
        }
    }

    .md\:w-1\/2 {
        width: 50%;
    }

    .md\:mb-0 {
        margin-bottom: 0;
    }

    .md\:pr-10 {
        padding-right: 2.5rem;
    }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-4 {
        -ms-grid-columns: 1fr 1fr 1fr 1fr;
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    /* IE11 大屏幕 Grid fallback */
    @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
        .lg\:grid-cols-4 > * {
            -ms-flex: 0 0 25%;
            flex: 0 0 25%;
            max-width: 25%;
        }
    }
}

/* 特殊样式 */
.whitespace-nowrap {
    white-space: nowrap;
}

/* 自定义高度类 */
.h-\[600px\] {
    height: 600px;
}

.min-h-\[1024px\] {
    min-height: 1024px;
}

/* 特殊宽度 */
.max-height-90vh {
    max-height: 90vh;
}

.width-90 {
    width: 90%;
}

/* 特殊背景图片 */
.bg-url-hero {
    background-image: url("../images/hero.jpg");
}

.fa-icon {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
    appearance: textfield;
}

/* IE11 appearance fallback */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    input[type="number"] {
        /* IE11 不支持 appearance，但可以通过其他方式隐藏 spinner */
    }

    input[type="number"]::-ms-clear {
        display: none;
    }
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

/* IE11 backdrop-filter fallback */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .modal {
        background-color: rgba(0, 0, 0, 0.7) !important;
    }
}

.modal.show {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-animation: fadeIn 0.3s ease-out;
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    max-width: 400px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04);
    -webkit-animation: slideIn 0.3s ease-out;
    animation: slideIn 0.3s ease-out;
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6b7280;
    width: 2rem;
    height: 2rem;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    border-radius: 50%;
    -webkit-transition: all 0.2s;
    transition: all 0.2s;
    cursor: pointer;
}

.modal-close:hover {
    color: #374151;
}

@-webkit-keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@-webkit-keyframes slideIn {
    from {
        opacity: 0;
        -webkit-transform: translateY(-20px) scale(0.95);
        transform: translateY(-20px) scale(0.95);
    }

    to {
        opacity: 1;
        -webkit-transform: translateY(0) scale(1);
        transform: translateY(0) scale(1);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        -webkit-transform: translateY(-20px) scale(0.95);
        transform: translateY(-20px) scale(0.95);
    }

    to {
        opacity: 1;
        -webkit-transform: translateY(0) scale(1);
        transform: translateY(0) scale(1);
    }
}

/* 浮动按钮样式 */
.floating-buttons {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 999;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    gap: 1rem;
    opacity: 0;
    visibility: hidden;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.floating-buttons.show {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translateY(0);
    transform: translateY(0);
}

.floating-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    font-size: 1.25rem;
    position: relative;
    overflow: hidden;
}

.floating-btn:hover {
    -webkit-transform: translateY(-2px);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.floating-btn:active {
    -webkit-transform: translateY(0);
    transform: translateY(0);
}

.back-to-top {
    background-color: #2563eb;
    color: white;
}

.back-to-top:hover {
    background-color: #1e40af;
}

.contact-us {
    background-color: #10b981;
    color: white;
}

.contact-us:hover {
    background-color: #059669;
}

/* 按钮提示文字 */
.floating-btn::before {
    content: attr(data-tooltip);
    position: absolute;
    right: 100%;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    margin-right: 0.5rem;
    pointer-events: none;
}

.floating-btn:hover::before {
    opacity: 1;
    visibility: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .floating-buttons {
        bottom: 1rem;
        right: 1rem;
        gap: 0.75rem;
    }

    .floating-btn {
        width: 48px;
        height: 48px;
        font-size: 1.125rem;
    }

    .floating-btn::before {
        display: none;
    }
}

/* 表单样式 */
input,
textarea {
    border: 1px solid #d1d5db;
    outline: none;
    font-family: inherit;
}

input:focus,
textarea:focus {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.border-gray-300 {
    border-color: #d1d5db;
}

.focus\:ring-2:focus {
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.focus\:ring-primary:focus {
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.focus\:border-transparent:focus {
    border-color: transparent;
}

.resize-none {
    resize: none;
}

.tracking-input {
    outline: none;
    transition: all 0.2s ease;
}

/* 加载动画 */
@-webkit-keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    to {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.animate-spin {
    -webkit-animation: spin 1s linear infinite;
    animation: spin 1s linear infinite;
}

/* 状态颜色 */
.bg-yellow-100 {
    background-color: #fef3c7;
}

.text-yellow-800 {
    color: #92400e;
}

.bg-red-100 {
    background-color: #fee2e2;
}

.text-red-800 {
    color: #991b1b;
}

/* 轨迹查询专用样式优化 */
.tracking-search-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.15);
    position: relative;
    overflow: hidden;
}

.tracking-search-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.tracking-search-content {
    position: relative;
    z-index: 1;
}

.tracking-input-group {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 1.5rem;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* IE11 backdrop-filter fallback */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .tracking-input-group {
        background: rgba(255, 255, 255, 0.98) !important;
        border: 1px solid rgba(0, 0, 0, 0.1) !important;
    }
}

.tracking-textarea {
    border: 2px solid transparent;
    background: #ffffff;
    border-radius: 8px;
    padding: 1rem;
    font-size: 0.95rem;
    line-height: 1.6;
    transition: all 0.3s ease;
    resize: vertical;
    min-height: 120px;
}

.tracking-textarea:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    outline: none;
}

.tracking-textarea::placeholder {
    color: #9ca3af;
    font-style: italic;
}

.tracking-search-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    padding: 1rem 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
    height: 5rem;
}

.tracking-search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.tracking-search-btn:active {
    transform: translateY(0);
}

.tracking-search-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.tracking-search-btn:hover::before {
    left: 100%;
}

.tracking-input-helper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.tracking-input-counter {
    color: #9ca3af;
}

.tracking-quick-actions {
    display: flex;
    gap: 0.5rem;
}

.tracking-quick-btn {
    background: none;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tracking-quick-btn:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
    color: #374151;
}

/* 结果卡片优化 */
.tracking-result-card {
    background: #ffffff;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tracking-result-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.tracking-result-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.tracking-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.tracking-status-badge::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0.75rem;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    transform: translateY(-50%);
}

.tracking-status-badge.status-reported::before {
    background: #3b82f6;
    animation: pulse-blue 2s infinite;
}

.tracking-status-badge.status-transit::before {
    background: #f59e0b;
    animation: pulse-yellow 2s infinite;
}

.tracking-status-badge.status-delivered::before {
    background: #10b981;
    animation: pulse-green 2s infinite;
}

.tracking-status-badge.status-exception::before {
    background: #ef4444;
    animation: pulse-red 2s infinite;
}

@keyframes pulse-blue {
    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

@keyframes pulse-yellow {
    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

@keyframes pulse-green {
    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

@keyframes pulse-red {
    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

/* 结果卡片信息项优化 */
.tracking-info-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: default;
}

.tracking-info-item:hover {
    background: rgba(102, 126, 234, 0.05);
}

.tracking-info-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    color: #6b7280;
    flex-shrink: 0;
}

.tracking-info-content {
    flex: 1;
    min-width: 0;
}

.tracking-info-label {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.125rem;
}

.tracking-info-value {
    font-weight: 500;
    color: #1f2937;
    word-break: break-all;
}

/* 操作按钮组 */
.tracking-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f3f4f6;
}

.tracking-action-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: #ffffff;
    color: #6b7280;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.tracking-action-btn:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    color: #374151;
    transform: translateY(-1px);
}

.tracking-action-btn i {
    margin-right: 0.375rem;
    font-size: 0.75rem;
}

/* 加载状态优化 */
.tracking-loading {
    text-align: center;
    padding: 3rem 1rem;
}

.tracking-loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.tracking-loading-text {
    color: #6b7280;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.tracking-loading-subtext {
    color: #9ca3af;
    font-size: 0.875rem;
}

/* 空状态优化 */
.tracking-empty {
    text-align: center;
    padding: 3rem 1rem;
}

.tracking-empty-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: #9ca3af;
    font-size: 1.5rem;
}

.tracking-empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.tracking-empty-description {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.tracking-empty-action {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tracking-empty-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
}

/* 结果统计信息 */
.tracking-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #f3f4f6;
}

.tracking-results-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
}

.tracking-results-count {
    font-size: 0.875rem;
    color: #6b7280;
    background: #ffffff;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
}

/* 通知系统样式 */
.tracking-notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 9999;
    max-width: 400px;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.tracking-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.tracking-notification-content {
    display: flex;
    align-items: center;
    font-weight: 500;
}

.tracking-notification-success {
    background: #10b981;
    color: white;
}

.tracking-notification-error {
    background: #ef4444;
    color: white;
}

.tracking-notification-warning {
    background: #f59e0b;
    color: white;
}

.tracking-notification-info {
    background: #3b82f6;
    color: white;
}

/* 输入计数器和快捷操作 */
.tracking-input-counter {
    font-size: 0.875rem;
    color: #6b7280;
}

.tracking-quick-actions {
    display: flex;
    gap: 0.5rem;
}

.tracking-quick-btn {
    background: none;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tracking-quick-btn:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
    color: #374151;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .tracking-input-group {
        padding: 1rem;
    }

    .tracking-textarea {
        min-height: 100px;
        font-size: 0.875rem;
    }

    .tracking-search-btn {
        width: 100%;
        margin-top: 1rem;
    }

    .tracking-result-card {
        padding: 1rem;
    }

    .tracking-actions {
        flex-direction: column;
    }

    .tracking-action-btn {
        justify-content: center;
    }

    .tracking-results-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .tracking-notification {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }

    .tracking-input-helper {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .tracking-quick-actions {
        align-self: flex-end;
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

.tracking-result-card {
    animation: slideInUp 0.5s ease-out forwards;
    opacity: 0;
}

.tracking-search-container {
    animation: fadeInScale 0.6s ease-out;
}

/* 悬停效果增强 */
.tracking-info-item:hover .tracking-info-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: scale(1.1);
}

.tracking-action-btn:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-color: #667eea;
    color: #667eea;
}

/* 加载状态优化 */
.tracking-loading-spinner {
    background: conic-gradient(from 0deg, #667eea, #764ba2, #667eea);
    border: none;
    border-radius: 50%;
    position: relative;
}

.tracking-loading-spinner::before {
    content: "";
    position: absolute;
    top: 3px;
    left: 3px;
    right: 3px;
    bottom: 3px;
    background: #f9fafb;
    border-radius: 50%;
}

/* 状态指示器增强 */
.tracking-status-badge {
    position: relative;
    overflow: visible;
}

.tracking-status-badge::after {
    content: "";
    position: absolute;
    top: 50%;
    right: -8px;
    width: 0;
    height: 0;
    border-left: 8px solid currentColor;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    transform: translateY(-50%);
    opacity: 0.3;
}

/* 输入框焦点效果增强 */
.tracking-textarea:focus {
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.15),
        0 0 0 4px rgba(102, 126, 234, 0.1);
}

/* 微交互动画 */
.tracking-quick-btn:active {
    transform: scale(0.95);
}

.tracking-action-btn:active {
    transform: translateY(1px);
}

.tracking-search-btn:disabled {
    cursor: not-allowed;
    transform: none !important;
}

/* 渐变文字效果 */
.tracking-search-container h2 {
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* IE11 渐变文字 fallback */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .tracking-search-container h2 {
        background: none !important;
        -webkit-text-fill-color: initial !important;
        color: #ffffff !important;
    }
}

/* 卡片边框动画 */
.tracking-result-card::before {
    transition: all 0.3s ease;
}

.tracking-result-card:hover::before {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    width: 6px;
}

/* 派送搜索专用样式 */
.delivery-search-container {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(16, 185, 129, 0.15);
    position: relative;
    overflow: hidden;
}

.delivery-search-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.delivery-search-content {
    position: relative;
    z-index: 1;
}

.delivery-input-group {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 1.5rem;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* IE11 backdrop-filter fallback */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .delivery-input-group {
        background: rgba(255, 255, 255, 0.98) !important;
        border: 1px solid rgba(0, 0, 0, 0.1) !important;
    }
}

.delivery-textarea {
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.875rem;
    line-height: 1.5;
    resize: vertical;
    transition: all 0.2s ease;
    font-family: inherit;
    background: #ffffff;
}

.delivery-textarea:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.delivery-textarea::placeholder {
    color: #9ca3af;
    line-height: 1.4;
}

.delivery-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.875rem;
    line-height: 1.5;
    transition: all 0.2s ease;
    font-family: inherit;
    background: #ffffff;
}

.delivery-input:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.delivery-input::placeholder {
    color: #9ca3af;
}

.delivery-input-helper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    font-size: 0.875rem;
}

.delivery-input-tips {
    display: flex;
    align-items: center;
    color: #6b7280;
}

.delivery-quick-actions {
    display: flex;
    gap: 0.5rem;
}

.delivery-quick-btn {
    background: none;
    border: 1px solid #d1d5db;
    color: #6b7280;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.delivery-quick-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
    color: #374151;
}

.delivery-search-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-height: 56px;
}

.delivery-search-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
    transform: translateY(-1px);
}

.delivery-search-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* 加载状态 */
.delivery-loading {
    text-align: center;
    padding: 3rem 2rem;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.delivery-loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #10b981;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.delivery-loading-text {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.delivery-loading-subtext {
    color: #6b7280;
    font-size: 0.875rem;
}

/* 结果展示 */
.delivery-results-header {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.delivery-results-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.delivery-results-info {
    color: #6b7280;
    font-size: 0.875rem;
}

/* 地址信息卡片 */
.delivery-address-info {
    margin-bottom: 1.5rem;
}

.delivery-address-card {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.delivery-address-card:hover {
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.delivery-address-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1rem;
    color: #1f2937;
}

.delivery-address-content {
    color: #4b5563;
    font-size: 0.875rem;
    line-height: 1.6;
    padding: 0.75rem;
    background: #f9fafb;
    border-radius: 8px;
    border-left: 4px solid #10b981;
}

/* 距离信息 */
.delivery-distance-info {
    margin-bottom: 1.5rem;
}

.delivery-distance-card {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 12px;
    padding: 1.5rem;
    color: white;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
}

.delivery-distance-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.delivery-distance-content {
    flex: 1;
}

.delivery-distance-label {
    font-size: 0.875rem;
    opacity: 0.9;
    margin-bottom: 0.25rem;
}

.delivery-distance-value {
    font-size: 1.5rem;
    font-weight: 700;
}

/* 价格表格容器 */
.delivery-price-container {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
}

.delivery-price-header {
    margin-bottom: 1.5rem;
}

.delivery-price-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.delivery-price-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.delivery-price-table-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.delivery-price-table-header th {
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
    white-space: nowrap;
}

.delivery-price-table-body td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid #f3f4f6;
    color: #4b5563;
    vertical-align: middle;
}

.delivery-price-table-body tr:hover {
    background: #f9fafb;
}

.delivery-price-table-body tr:last-child td {
    border-bottom: none;
}

/* 价格高亮 */
.delivery-price-highlight {
    font-weight: 700;
    color: #10b981;
    font-size: 1rem;
}

.delivery-price-currency {
    font-size: 0.75rem;
    color: #6b7280;
    margin-left: 0.25rem;
}

/* 咨询按钮 */
.delivery-consult-btn {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.delivery-consult-btn:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

/* 地图容器 */
.delivery-map-container {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.delivery-map-header {
    margin-bottom: 1rem;
}

.delivery-map-wrapper {
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e5e7eb;
    height: 400px;
    position: relative;
}

.delivery-map-iframe {
    width: 100%;
    height: 100%;
    border: none;
    display: block;
}

/* 无结果状态 */
.delivery-empty {
    text-align: center;
    padding: 3rem 2rem;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.delivery-empty-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: #9ca3af;
}

.delivery-empty-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.delivery-empty-description {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.delivery-empty-action {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.delivery-empty-action:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
    color: white;
    text-decoration: none;
}

/* 渐变文字效果 */
.delivery-search-container h2 {
    background: linear-gradient(135deg, #ffffff 0%, #e0f2fe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* IE11 渐变文字 fallback */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .delivery-search-container h2 {
        background: none !important;
        -webkit-text-fill-color: initial !important;
        color: #ffffff !important;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .delivery-input-group {
        padding: 1rem;
    }

    .delivery-textarea {
        min-height: 100px;
        font-size: 0.875rem;
    }

    .delivery-search-btn {
        width: 100%;
        margin-top: 1rem;
    }

    .delivery-input-helper {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .delivery-quick-actions {
        align-self: flex-end;
    }

    .delivery-address-card {
        padding: 1rem;
    }

    .delivery-distance-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .delivery-distance-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    /* 手机端派送价格表格改为卡片式布局 */
    .delivery-price-table-container {
        overflow: visible;
        border: none;
    }

    .delivery-price-table {
        display: block;
        width: 100%;
    }

    .delivery-price-table-header {
        display: none;
    }

    .delivery-price-table-body {
        display: block;
    }

    .delivery-price-table-body tr {
        display: block;
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        margin-bottom: 1rem;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .delivery-price-table-body tr:hover {
        background: #ffffff;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .delivery-price-table-body td {
        display: block;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f3f4f6;
        position: relative;
        padding-left: 5.5rem;
    }

    .delivery-price-table-body td:last-child {
        border-bottom: none;
        padding-left: 0;
        text-align: center;
        margin-top: 0.5rem;
    }

    .delivery-price-table-body td:before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        top: 0.5rem;
        font-weight: 600;
        color: #374151;
        width: 5rem;
        text-align: left;
    }

    /* 为每个列添加标签 */
    #priceTableBody td:nth-child(1):before {
        content: "车型:";
    }

    #priceTableBody td:nth-child(2):before {
        content: "载重:";
    }

    #priceTableBody td:nth-child(3):before {
        content: "距离:";
    }

    #priceTableBody td:nth-child(4):before {
        content: "货物单价:";
    }

    #priceTableBody td:nth-child(5):before {
        content: "派送报价:";
    }

    #priceTableBody td:nth-child(6):before {
        content: "";
    }

    #priceTableBodyLCL td:nth-child(1):before {
        content: "派送方式:";
    }

    #priceTableBodyLCL td:nth-child(2):before {
        content: "距离:";
    }

    #priceTableBodyLCL td:nth-child(3):before {
        content: "派送报价:";
    }

    #priceTableBodyLCL td:nth-child(4):before {
        content: "";
    }

    #priceTableBodyAppend td:nth-child(1):before {
        content: "附加费项目:";
    }

    #priceTableBodyAppend td:nth-child(2):before {
        content: "收费标准:";
    }

    #priceTableBodyAppend td:nth-child(3):before {
        content: "";
    }

    #priceTableBodyAppend td:nth-child(4):before {
        content: "";
    }

    #priceTableBodyAppend td:last-child,
    #priceTableBodyLCL td:last-child {
        display: block;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f3f4f6;
        position: relative;
        padding-left: 5.5rem;
        text-align: left;
    }

    .delivery-map-wrapper {
        height: 300px;
    }
}

/* 动画效果 */
.delivery-search-container {
    animation: fadeInScale 0.6s ease-out;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 表格样式的搜索结果 */
.tracking-table-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.tracking-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.tracking-table-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.tracking-table-header th {
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.875rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.tracking-table-header th:not(:last-child)::after {
    content: "";
    position: absolute;
    right: 0;
    top: 25%;
    bottom: 25%;
    width: 1px;
    background: rgba(255, 255, 255, 0.2);
}

/* 表格列宽度 */
.tracking-table th:nth-child(1) {
    width: auto;
}

.tracking-table th:nth-child(2) {
    min-width: 6rem;
}

.tracking-table th:nth-child(3) {
    min-width: 6rem;
}

.tracking-table th:nth-child(4) {
    min-width: 3rem;
}

.tracking-table th:nth-child(5) {
    min-width: 10rem;
}

.tracking-table-body tr {
    border-bottom: 1px solid #f3f4f6;
    transition: all 0.2s ease;
}

.tracking-table-body tr:hover {
    background: rgba(102, 126, 234, 0.02);
}

.tracking-table-body tr:last-child {
    border-bottom: none;
}

.tracking-table-body td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-right: 1px solid #f9fafb;
}

.tracking-table-body td:last-child {
    border-right: none;
}

.tracking-table-waybill {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.tracking-table-system {
    font-size: 0.75rem;
    color: #6b7280;
}

.tracking-table-status {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 500;
    position: relative;
}

.tracking-table-status.status-reported {
    background: #dbeafe;
    color: #1e40af;
}

.tracking-table-status.status-reported::before {
    background: #3b82f6;
    animation: pulse-blue 2s infinite;
}

.tracking-table-status.status-transit {
    background: #fef3c7;
    color: #92400e;
}

.tracking-table-status.status-transit::before {
    background: #f59e0b;
    animation: pulse-yellow 2s infinite;
}

.tracking-table-status.status-delivered {
    background: #dcfce7;
    color: #166534;
}

.tracking-table-status.status-delivered::before {
    background: #10b981;
    animation: pulse-green 2s infinite;
}

.tracking-table-status.status-exception {
    background: #fee2e2;
    color: #991b1b;
}

.tracking-table-status.status-exception::before {
    background: #ef4444;
    animation: pulse-red 2s infinite;
}

.tracking-table-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.tracking-table-action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: #ffffff;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    font-size: 0.75rem;
}

.tracking-table-action-btn:hover {
    background: #f9fafb;
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-1px);
}

.tracking-table-action-btn:active {
    transform: translateY(0);
}

.tracking-table-info {
    color: #374151;
    line-height: 1.4;
}

.tracking-table-info-secondary {
    color: #6b7280;
    font-size: 0.75rem;
    margin-top: 0.125rem;
}

/* 表格响应式设计 */
@media (max-width: 1024px) {
    .tracking-table-container {
        overflow-x: auto;
    }

    .tracking-table {
        min-width: 800px;
    }

    .tracking-table-header th,
    .tracking-table-body td {
        padding: 0.75rem 0.5rem;
    }
}

@media (max-width: 768px) {
    .tracking-table {
        min-width: 600px;
    }

    .tracking-table-header th,
    .tracking-table-body td {
        padding: 0.5rem 0.375rem;
        font-size: 0.75rem;
    }

    .tracking-table-waybill {
        font-size: 0.875rem;
    }

    .tracking-table-actions {
        flex-direction: column;
        gap: 0.25rem;
    }

    .tracking-table-action-btn {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.625rem;
    }
}

/* 表格加载动画 */
.tracking-table-row-enter {
    animation: tableRowSlideIn 0.4s ease-out forwards;
    opacity: 0;
}

@keyframes tableRowSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 描述行样式 */
.tracking-description-row {
    background: #f8fafc !important;
    animation: slideDown 0.3s ease-out;
}

.tracking-description-row:hover {
    background: #f1f5f9 !important;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }

    to {
        opacity: 1;
        max-height: 200px;
    }
}

/* 可点击行的样式 */
.tracking-table-body tr[data-description] {
    cursor: pointer;
}

.tracking-table-body tr[data-description]:hover {
    background: rgba(102, 126, 234, 0.05) !important;
}

/* 表格排序功能 */
.tracking-table-header th {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    position: relative;
}

.tracking-table-header th:hover {
    background: rgba(255, 255, 255, 0.1);
}

.tracking-table-header th.sortable::after {
    content: "\f0dc";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    position: absolute;
    right: 0.5rem;
    opacity: 0.5;
    font-size: 0.75rem;
}

.tracking-table-header th.sort-asc::after {
    content: "\f0de";
    opacity: 1;
}

.tracking-table-header th.sort-desc::after {
    content: "\f0dd";
    opacity: 1;
}

/* 表格空状态 */
.tracking-table-empty {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
}

.tracking-table-empty td {
    border: none !important;
}

/* 表格加载状态 */
.tracking-table-loading {
    text-align: center;
    padding: 2rem 1rem;
}

.tracking-table-loading td {
    border: none !important;
}

/* 状态统计 */
.tracking-status-summary {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.tracking-status-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    font-size: 0.875rem;
}

.tracking-status-item-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.tracking-status-item-dot.reported {
    background: #3b82f6;
}

.tracking-status-item-dot.transit {
    background: #f59e0b;
}

.tracking-status-item-dot.delivered {
    background: #10b981;
}

.tracking-status-item-dot.exception {
    background: #ef4444;
}

.tracking-status-item-dot.unknown {
    background: #6b7280;
}

/* 表格工具栏 */
.tracking-table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.tracking-table-search {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tracking-table-search input {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    width: 200px;
}

.tracking-table-search input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.tracking-table-export {
    display: flex;
    gap: 0.5rem;
}

.tracking-table-export-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: #ffffff;
    color: #374151;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tracking-table-export-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

/* 间距工具类补充 */
.space-y-6 > * + * {
    margin-top: 1.5rem;
}

/* IE11 特定样式修复 */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    /* IE11+ Flexbox 修复 */
    .flex {
        display: -ms-flexbox !important;
    }

    .flex-col {
        -ms-flex-direction: column !important;
    }

    .items-center {
        -ms-flex-align: center !important;
    }

    .items-start {
        -ms-flex-align: start !important;
    }

    .justify-center {
        -ms-flex-pack: center !important;
    }

    .justify-between {
        -ms-flex-pack: justify !important;
    }

    .flex-grow {
        -ms-flex: 1 !important;
        -ms-flex-positive: 1 !important;
    }

    /* Gap 属性 fallback */
    .space-x-4 > * + * {
        margin-left: 1rem !important;
    }

    .space-x-8 > * + * {
        margin-left: 2rem !important;
    }

    .space-y-3 > * + * {
        margin-top: 0.75rem !important;
    }

    .space-y-6 > * + * {
        margin-top: 1.5rem !important;
    }

    /* Transform 属性修复 */
    .hover\:-translate-y-1:hover {
        -ms-transform: translateY(-0.25rem) !important;
    }

    /* User-select 修复 */
    .tracking-table-header th {
        -ms-user-select: none !important;
    }

    /* Transition 属性修复 */
    .transition {
        -ms-transition: all 0.2s ease !important;
    }

    .transition-transform {
        -ms-transition: -ms-transform 0.3s ease !important;
    }

    .duration-300 {
        -ms-transition-duration: 0.3s !important;
    }

    /* Animation 属性修复 */
    .animate-spin {
        -ms-animation: spin 1s linear infinite !important;
    }

    /* 修复 IE11 的 flexbox 问题 */
    .container {
        max-width: 1200px;
        margin: 0 auto;
    }

    /* IE11 按钮样式修复 */
    button {
        border: none;
        outline: none;
    }

    /* IE11 输入框样式修复 */
    input,
    textarea {
        border: 1px solid #d1d5db;
        outline: none;
    }

    /* IE11 浮动按钮修复 */
    .floating-buttons {
        display: -ms-flexbox !important;
        -ms-flex-direction: column !important;
        flex-direction: column !important;
    }

    .floating-btn {
        display: -ms-flexbox !important;
        -ms-flex-align: center !important;
        -ms-flex-pack: center !important;
    }

    /* 修复 calc() 函数兼容性 */
    .py-2\.5 {
        padding-top: 10px !important;
        padding-bottom: 10px !important;
    }

    .mt-0\.5 {
        margin-top: 2px !important;
    }

    /* 修复 CSS 变量不支持的问题 */
    .h-\[600px\] {
        height: 600px !important;
    }

    .min-h-\[1024px\] {
        min-height: 1024px !important;
    }

    /* 修复 vw/vh 单位兼容性 */
    .max-height-90vh {
        max-height: 648px !important;
        /* 90% of 720px */
    }

    /* 修复 border-radius 在某些情况下的问题 */
    .rounded-full {
        border-radius: 50% !important;
    }

    /* 修复 overflow 属性 */
    .overflow-hidden {
        overflow: hidden !important;
    }

    .overflow-y-auto {
        overflow-y: auto !important;
    }

    /* 修复 white-space 属性 */
    .whitespace-nowrap {
        white-space: nowrap !important;
    }

    /* 修复 resize 属性 */
    .resize-none {
        resize: none !important;
    }

    /* 修复 position sticky */
    .sticky {
        position: -webkit-sticky !important;
        position: sticky !important;
    }

    /* 修复 min/max 函数 */
    .min-h-1024px {
        min-height: 1024px !important;
    }

    .max-w-2xl {
        max-width: 42rem !important;
    }

    .max-w-400px {
        max-width: 400px !important;
    }
}

.help_step {
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border-radius: 50%;
    background: #667eea;
    color: white;
}

.mobileMenuBox {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 1.5rem;
    background-color: #ffffff;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border-top: 1px solid #e5e7eb;
    position: absolute;
    width: 100%;
    z-index: 50;
}

.mobileMenuBox a {
    padding: 0.75rem 1rem;
    color: #4b5563;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.mobileMenuBox a:hover {
    background-color: #f3f4f6;
    color: #1f2937;
    transform: translateX(4px);
}

/* 在中等屏幕尺寸(768px)以上隐藏移动端菜单 */
@media (min-width: 768px) {
    #mobileMenuButton {
        display: none;
    }
}

.items-search-container {
    background: linear-gradient(135deg, #1070b9 0%, #055096 100%);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(16, 185, 129, 0.15);
    position: relative;
    overflow: hidden;
}

.items-search-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.items-search-content {
    position: relative;
    z-index: 1;
}

.items-input-group {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 1.5rem;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.items-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.875rem;
    line-height: 1.5;
    transition: all 0.2s ease;
    font-family: inherit;
    background: #ffffff;
}

.items-input:focus {
    outline: none;
    border-color: #1070b9;
    box-shadow: 0 0 0 3px rgba(16, 112, 185, 0.1);
}

.items-input::placeholder {
    color: #9ca3af;
}

.items-input-helper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    font-size: 0.875rem;
}

.items-input-tips {
    display: flex;
    align-items: center;
    color: #6b7280;
}

.items-quick-actions {
    display: flex;
    gap: 0.5rem;
}

.items-quick-btn {
    background: none;
    border: 1px solid #d1d5db;
    color: #6b7280;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.items-quick-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
    color: #374151;
}

.items-search-btn {
    background: linear-gradient(135deg, #1070b9 0%, #055096 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(16, 112, 185, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-height: 56px;
}

.items-search-btn:hover {
    background: linear-gradient(135deg, #055096 0%, #033c6e 100%);
    box-shadow: 0 6px 16px rgba(16, 112, 185, 0.4);
    transform: translateY(-1px);
}

.items-search-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* 加载状态 */
.items-loading {
    text-align: center;
    padding: 3rem 2rem;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.items-loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #1070b9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.items-loading-text {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.items-loading-subtext {
    color: #6b7280;
    font-size: 0.875rem;
}

/* 结果展示 */
.items-results-header {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.items-results-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
}

.items-results-count {
    color: #6b7280;
    font-size: 0.875rem;
    background: #f3f4f6;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

/* 表格样式 */
.items-table-container {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.items-table-header {
    background: linear-gradient(135deg, #1070b9 0%, #055096 100%);
    color: white;
}

.items-table-header th {
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.875rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.items-table-body tr {
    border-bottom: 1px solid #f3f4f6;
    transition: all 0.2s ease;
}

.items-table-body tr:hover {
    background: rgba(16, 112, 185, 0.02);
}

.items-table-body tr:last-child {
    border-bottom: none;
}

.items-table-body td {
    padding: 1rem 0.75rem;
    color: #4b5563;
}

/* 无结果状态 */
.items-empty {
    text-align: center;
    padding: 3rem 2rem;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.items-empty-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: #9ca3af;
}

.items-empty-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.items-empty-description {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.items-empty-action {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #1070b9 0%, #055096 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(16, 112, 185, 0.3);
}

.items-empty-action:hover {
    background: linear-gradient(135deg, #055096 0%, #033c6e 100%);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(16, 112, 185, 0.4);
    color: white;
    text-decoration: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .items-input-group {
        padding: 1rem;
    }

    .items-input {
        font-size: 0.875rem;
    }

    .items-search-btn {
        width: 100%;
        margin-top: 1rem;
    }

    .items-input-helper {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .items-quick-actions {
        align-self: flex-end;
    }

    .items-results-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    /* 手机端表格改为卡片式布局 */
    .items-table-container {
        overflow: visible;
        border: none;
        box-shadow: none;
    }

    .items-table {
        display: block;
        width: 100%;
    }

    .items-table-header {
        display: none;
    }

    .items-table-body {
        display: block;
    }

    .items-table-body tr {
        display: block;
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        margin-bottom: 1rem;
        padding: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .items-table-body tr:hover {
        background: #ffffff;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .items-table-body td {
        display: block;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f3f4f6;
        position: relative;
        padding-left: 5rem;
    }

    .items-table-body td:last-child {
        border-bottom: none;
    }

    .items-table-body td:before {
        content: attr(data-label);
        position: absolute;
        left: 0;
        top: 0.5rem;
        font-weight: 600;
        color: #374151;
        width: 4.5rem;
        text-align: left;
    }

    /* 为每个列添加标签 */
    .items-table-body td:nth-child(1):before {
        content: "分类:";
    }

    .items-table-body td:nth-child(2):before {
        content: "品名:";
    }

    .items-table-body td:nth-child(3):before {
        content: "材质:";
    }

    .items-table-body td:nth-child(4):before {
        content: "HS编码:";
    }

    .items-table-body td:nth-child(5):before {
        content: "备注:";
    }
}
