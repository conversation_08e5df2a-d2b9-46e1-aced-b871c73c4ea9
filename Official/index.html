<!DOCTYPE html>
<html lang="zh-CN" ver=20250728>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- IE10 兼容性设置 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="msapplication-tap-highlight" content="no">
    <title>东诚易胜国际 - 泰国专线物流专家</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- IE11 兼容性 polyfills -->
    <!--[if IE]>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/respond.js/1.4.2/respond.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/classlist/1.2.20171210/classList.min.js"></script>
    <![endif]-->

    <!-- IE11 特定样式 -->
    <!--[if IE]>
    <style>
        /* IE11 Flexbox 修复 */
        .ie-flexbox-fix {
            display: -ms-flexbox !important;
            -ms-flex-direction: row;
            -ms-flex-wrap: wrap;
            -ms-flex-pack: justify;
            -ms-flex-align: center;
        }
        /* IE11 Grid 修复 */
        .ie-grid-fix {
            display: -ms-grid !important;
            -ms-grid-columns: 1fr;
        }
    </style>
    <![endif]-->
    <link rel="stylesheet" href="css/style.css?ver=20250728">

    <script>
        // 检测IE浏览器并显示警告
        function detectIE() {
            var ua = window.navigator.userAgent;
            var msie = ua.indexOf('MSIE ');
            var trident = ua.indexOf('Trident/');

            if (msie > 0 || trident > 0) {
                document.querySelector('.ie-warning').classList.remove('hidden');
            }
        }
        // 页面加载完成后执行检测
        window.addEventListener('load', detectIE);
    </script>


</head>

<body class="min-h-1024px bg-gray-50 font-sans text-gray-800">




    <!-- 顶部导航 -->
    <header class="sticky top-0 z-50 bg-white shadow-md">
        <div style="background-color: yellow;width: 100%;text-align: center;z-index: 1000;line-height: 100px;"
            class="ie-warning hidden">
            您正在使用过时的Internet Explorer浏览器，部分功能可能无法正常使用。建议升级到<a href="https://www.microsoft.com/edge" target="_blank"
                class="underline font-bold">Microsoft Edge</a>或使用<a href="https://www.google.cn/chrome/" target="_blank"
                class="underline font-bold">Google Chrome</a>等现代浏览器。
        </div>
        <div class="container mx-auto px-6 py-4 flex items-center justify-between">
            <!-- IE浏览器提示 - 您的浏览器版本过低，建议使用Chrome、Firefox或Edge等现代浏览器 -->

            <div class="flex items-center">
                <a href="./" class="flex items-center text-2xl font-logo text-primary">
                    <img src="./images/logo.svg" alt="logo" class="h-6">
                </a>
                <nav class="hidden md:flex ml-12 space-x-8 items-center">
                    <a href="#index" pagegoto="index" class="font-medium hover:text-primary transition">首页</a>
                    <a href="#track" pagegoto="track" class="font-medium hover:text-primary transition">轨迹查询</a>
                    <a href="#search" pagegoto="search" class="font-medium hover:text-primary transition">海派查询</a>
                    <a href="#items" pagegoto="items" class="font-medium hover:text-primary transition">产品搜索</a>
                </nav>
            </div>
            <div class="flex items-center space-x-4">
                <a href="tel:18025444422"
                    class="hidden md:flex items-center text-gray-600 hover:text-primary transition">
                    <i class="fas fa-phone-alt fa-icon w-5 h-5 mr-2 mt-0.5"></i>
                    <span>咨询热线 18025444422</span>
                </a>
                <button type="button" onclick="openModal()"
                    class="hidden md:flex rounded-button whitespace-nowrap bg-primary hover:bg-secondary text-white px-6 py-2.5 transition items-center">
                    <i class="fas fa-comment-dots fa-icon w-4 h-4 mr-2"></i>
                    关注我们
                </button>
                <!-- 移动端菜单按钮 -->
                <button type="button" id="mobileMenuButton" class="text-gray-600 hover:text-primary transition">
                    <i class="fas fa-bars fa-icon w-6 h-6" id="menuIcon"></i>
                </button>
            </div>


        </div>
        <!-- 移动端菜单 -->
        <div class=" hidden" id="mobileMenu">
            <div class="mobileMenuBox">
                <a href="#index" pagegoto="index" class="font-medium hover:text-primary transition">首页</a>
                <a href="#track" pagegoto="track" class="font-medium hover:text-primary transition">轨迹查询</a>
                <a href="#search" pagegoto="search" class="font-medium hover:text-primary transition">海派查询</a>
                <a href="#items" pagegoto="items" class="font-medium hover:text-primary transition">产品搜索</a>
            </div>
        </div>
    </header>




    <div pages="items">
        <!-- 派送搜索页面 -->
        <section class="py-20 bg-gray-50">
            <div class="container mx-auto px-6">
                <!-- 搜索界面 -->
                <div class="items-search-container max-w-4xl mx-auto">
                    <div class="items-search-content">
                        <div class="text-center mb-8">
                            <h2 class="text-3xl font-bold text-white mb-2">品名搜索</h2>
                            <p class="text-blue-100">输入产品品名搜索属于那个分类</p>
                        </div>
                        <div class="items-input-group">
                            <div class="grid md:grid-cols-4 gap-6">
                                <div class="md:col-span-3">
                                    <input type="text" id="itemKeyword" placeholder="请输入产品名称或关键词"
                                        class="items-input w-full">
                                    <div class="items-input-helper">
                                        <div class="items-input-tips">
                                            <i class="fas fa-info-circle text-blue-400 mr-2"></i>
                                            <span class="text-sm">请输入产品名称,如:衣服、裤子等</span>
                                        </div>
                                        <div class="items-quick-actions">
                                            <button type="button" class="items-quick-btn" onclick="clearItemsInput()">
                                                <i class="fas fa-times"></i> 清空
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <button type="button" onclick="searchItems()" class="items-search-btn w-full"
                                        id="itemsSearchButton">
                                        <span>查询分类</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载状态 -->
                <div id="itemsLoadingState" class="hidden max-w-4xl mx-auto mt-8">
                    <div class="items-loading">
                        <div class="items-loading-spinner"></div>
                        <div class="items-loading-text">正在查询分类信息</div>
                        <div class="items-loading-subtext">请稍候，正在为您搜索相关产品...</div>
                    </div>
                </div>

                <!-- 查询结果 -->
                <div id="itemsResults" class="hidden max-w-6xl mx-auto mt-8">


                    <div class="items-table-container">
                        <table class="items-table">
                            <thead class="items-table-header">
                                <tr>
                                    <th>分类</th>
                                    <th>品名</th>
                                    <th>类似品名</th>
                                    <th>HS编码</th>
                                    <th>备注</th>
                                </tr>
                            </thead>
                            <tbody class="items-table-body" id="itemsContainer">
                                <!-- 结果将通过JavaScript动态插入 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 无结果状态 -->
                <div id="itemsNoResults" class="hidden max-w-4xl mx-auto mt-8">
                    <div class="items-empty">
                        <div class="items-empty-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="items-empty-title">未找到相关信息</div>
                        <div class="items-empty-description">
                            请检查输入的产品名称是否正确，或尝试以下操作：<br>
                            • 使用更简单的关键词<br>
                            • 检查是否有多余的空格<br>
                            • 联系客服获取帮助
                        </div>
                        <a href="#" onclick="openModal(); return false;" class="items-empty-action">
                            <i class="fas fa-headset mr-2"></i>
                            联系客服
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <div pages="search">
        <!-- 派送搜索页面 -->
        <section class="py-20 bg-gray-50">
            <div class="container mx-auto px-6">
                <!-- 搜索界面 -->
                <div class="delivery-search-container max-w-4xl mx-auto">
                    <div class="delivery-search-content">
                        <div class="text-center mb-8">
                            <h2 class="text-3xl font-bold text-white mb-2">海派搜索</h2>
                            <p class="text-blue-100">输入目的地地址，获取派送运费报价和距离信息，需要确认地址格式正确！</p>
                        </div>

                        <div class="delivery-input-group">
                            <div class="grid md:grid-cols-4 gap-6">
                                <div class="md:col-span-3">
                                    <textarea id="destinationAddress"
                                        placeholder="请输入目的地详细地址&#10;例如：456/4-6 ต.หนองปรือ Bang Lamung District, Chon Buri 20150 THAILAND"
                                        class="delivery-textarea w-full" rows="4"></textarea>
                                    <div class="delivery-input-helper">
                                        <div class="delivery-input-tips">
                                            <i class="fas fa-info-circle text-blue-400 mr-2"></i>
                                            <span class=" text-sm">请输入完整的泰国地址，包括省份和邮编</span>
                                        </div>
                                        <div class="delivery-quick-actions">
                                            <button type="button" class="delivery-quick-btn"
                                                onclick="clearDeliveryInput()">
                                                <i class="fas fa-times"></i> 清空
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <button type="button" onclick="searchDeliveryPrice()"
                                        class="delivery-search-btn w-full" id="deliverySearchButton">
                                        <span>查询报价</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载状态 -->
                <div id="deliveryLoadingState" class="hidden max-w-4xl mx-auto mt-8">
                    <div class="delivery-loading">
                        <div class="delivery-loading-spinner"></div>
                        <div class="delivery-loading-text">正在查询报价信息</div>
                        <div class="delivery-loading-subtext">请稍候，正在为您计算运费和距离...</div>
                    </div>
                </div>

                <!-- 查询结果 -->
                <div id="deliveryResults" class="hidden max-w-6xl mx-auto mt-8">

                    <!-- 价格表格 -->
                    <div class="delivery-price-container">
                        <div class="delivery-price-header">
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">派送运费报价</h3>
                            <p class="text-gray-600 text-sm text-red-500 font-bold">以下价格仅供参考，实际地址+价格请联系客服确认</p>
                            <p class="text-gray-600 text-sm">导航距离计算：<span id="distanceValue"
                                    class="text-primary font-bold"></span></p>
                            <p class="text-gray-600 text-sm">泰国发货地址：<span id="startAddress"
                                    class="text-red-500 font-bold"></span></p>
                            <p class="text-gray-600 text-sm">泰国收货地址：<span id="endAddress"
                                    class="text-green-500 font-bold"></span></p>



                        </div>
                        <div class="delivery-price-table-container">
                            <table class="delivery-price-table">
                                <thead class="delivery-price-table-header">
                                    <tr>
                                        <th style="min-width: 10rem;">包车派送车型</th>
                                        <th>载重</th>
                                        <th>距离(KM)</th>
                                        <th>派送报价 (人民币)</th>

                                    </tr>
                                </thead>
                                <tbody class="delivery-price-table-body" id="priceTableBody">
                                    <!-- 价格数据将通过JavaScript动态插入 -->
                                </tbody>
                            </table>
                        </div>

                        <div class="delivery-price-table-container" style="margin-top: 2rem;">
                            <table class="delivery-price-table">
                                <thead class="delivery-price-table-header">
                                    <tr>
                                        <th style="min-width: 10rem;">拼车派送</th>
                                        <th>距离(KM)</th>
                                        <th>派送报价 (人民币)</th>

                                    </tr>
                                </thead>
                                <tbody class="delivery-price-table-body" id="priceTableBodyLCL">
                                    <!-- 价格数据将通过JavaScript动态插入 -->
                                </tbody>
                                <thead class="delivery-price-table-header">
                                    <tr>
                                        <th style="min-width: 10rem;">海运超大件附加费</th>
                                        <th colspan="3">明细</th>

                                    </tr>
                                </thead>
                                <tbody class="delivery-price-table-body" id="priceTableBodyAppend">
                                    <!-- 价格数据将通过JavaScript动态插入 -->
                                </tbody>


                            </table>
                        </div>
                    </div>


                </div>

                <!-- 无结果状态 -->
                <div id="deliveryNoResults" class="hidden max-w-4xl mx-auto mt-8">
                    <div class="delivery-empty">
                        <div class="delivery-empty-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="delivery-empty-title" id="deliveryNoResultsTitle">未找到相关信息</div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    <div pages="track">
        <!-- 轨迹查询页面 -->
        <section class="py-20 bg-gray-50">
            <div class="container mx-auto px-6">
                <!-- 查询界面 -->
                <div class="tracking-search-container max-w-4xl mx-auto">
                    <div class="tracking-search-content">
                        <div class="text-center mb-8">
                            <h2 class="text-3xl font-bold text-white mb-2">轨迹查询</h2>
                            <p class="text-blue-100">输入运单号码，实时查询物流轨迹信息</p>
                        </div>

                        <div class="tracking-input-group">
                            <div class="grid md:grid-cols-4 gap-6">
                                <div class="md:col-span-3">
                                    <textarea id="trackingNumbers"
                                        placeholder="请输入运单号码，支持批量查询（每行一个号码）&#10;例如：&#10;DC2024001&#10;DC2024002&#10;DC2024003"
                                        class="tracking-textarea w-full" rows="5"></textarea>
                                    <div class="tracking-input-helper">
                                        <div class="tracking-input-counter">
                                            <span id="trackingCounter">0</span> 个运单号
                                        </div>
                                        <div class="tracking-quick-actions">
                                            <button type="button" class="tracking-quick-btn"
                                                onclick="clearTrackingInput()">
                                                <i class="fas fa-times"></i> 清空
                                            </button>

                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <button type="button" onclick="searchTrackingImproved()"
                                        class="tracking-search-btn w-full" id="searchButton">
                                        <span>查询轨迹</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>





                <!-- 加载状态 -->
                <div id="loadingState" class="hidden max-w-4xl mx-auto mt-8">
                    <div class="tracking-loading">
                        <div class="tracking-loading-spinner"></div>
                        <div class="tracking-loading-text">正在查询轨迹信息</div>
                        <div class="tracking-loading-subtext">请稍候，正在为您获取最新的物流状态...</div>
                    </div>
                </div>

                <!-- 查询结果 -->
                <div id="trackingResults" class="hidden max-w-6xl mx-auto mt-8">
                    <div class="tracking-results-header">
                        <div class="tracking-results-title">查询结果</div>
                        <div class="tracking-results-count" id="resultsCount">找到 0 条记录</div>
                    </div>


                    <div class="tracking-table-container">
                        <table class="tracking-table">
                            <thead class="tracking-table-header">
                                <tr>
                                    <th>查询单号</th>
                                    <th>轨迹</th>
                                </tr>
                            </thead>
                            <tbody class="tracking-table-body" id="resultsContainer">
                                <!-- 结果将通过JavaScript动态插入 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 无结果状态 -->
                <div id="noResults" class="hidden max-w-4xl mx-auto mt-8">
                    <div class="tracking-empty">
                        <div class="tracking-empty-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="tracking-empty-title">未找到相关信息</div>
                        <div class="tracking-empty-description">
                            请检查运单号码是否正确，或尝试以下操作：<br>
                            • 确认运单号格式正确<br>
                            • 检查是否有多余的空格<br>
                            • 联系客服获取帮助
                        </div>
                        <a href="#" onclick="openModal(); return false;" class="tracking-empty-action">
                            <i class="fas fa-headset mr-2"></i>
                            联系客服
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </div>


    <div pages="index" style="display: block;">
        <!-- Hero 区域 -->
        <section class="relative bg-gradient-to-r h-600px overflow-hidden" id="home">
            <div class="absolute inset-0 bg-url-hero bg-cover bg-center opacity-20"></div>
            <div class="container mx-auto px-6 h-full flex items-center relative z-10">
                <div class="max-w-2xl">
                    <h1 class="text-5xl font-bold text-gray-900 mb-6">专业泰国专线物流服务</h1>
                    <p class="text-xl text-gray-700 mb-8">东诚易胜国际汇聚10+年行业经验的专业团队，提供安全、高效、经济的中泰跨境陆运与海运专线服务，助力您的业务拓展东南亚市场。
                    </p>
                    <div class="flex space-x-4">
                        <button type="button" onclick="openModal()"
                            class="rounded-button whitespace-nowrap bg-primary hover:bg-secondary text-white px-8 py-3 text-lg transition">
                            立即咨询
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 关于我们 -->
        <section class="py-20 bg-white" id="about">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">关于东诚易胜国际</h2>
                    <div class="w-20 h-1 bg-primary mx-auto"></div>
                </div>
                <div class="flex flex-col md:flex-row items-center">
                    <div class="md:w-1/2 mb-10 md:mb-0 md:pr-10">
                        <h3 class="text-2xl font-semibold text-gray-800 mb-6">资深团队专注中泰跨境物流</h3>
                        <p class="text-gray-600 mb-6">
                            东诚易胜国际成立于2025年，总部位于深圳，是一家专业从事中国至泰国跨境物流运输的综合服务商。公司核心团队成员拥有10+年丰富的跨境物流行业经验，配备自营车队、专业报关团队和泰国本地配送网络，为客户提供一站式物流解决方案。
                        </p>
                        <p class="text-gray-600 mb-8">
                            我们秉承"诚信、专业、高效"的服务理念，凭借团队深厚的行业积淀，已成功为超过500家企业提供优质物流服务，货物安全送达率高达99.9%。</p>
                        <div class="grid grid-cols-2 gap-6">
                            <div class="bg-blue-50 p-6 rounded-lg">
                                <div class="text-4xl font-bold text-primary mb-2">10+</div>
                                <div class="text-gray-600">行业经验</div>
                            </div>
                            <div class="bg-blue-50 p-6 rounded-lg">
                                <div class="text-4xl font-bold text-primary mb-2">500+</div>
                                <div class="text-gray-600">服务客户</div>
                            </div>
                            <div class="bg-blue-50 p-6 rounded-lg">
                                <div class="text-4xl font-bold text-primary mb-2">99.9%</div>
                                <div class="text-gray-600">送达率</div>
                            </div>
                            <div class="bg-blue-50 p-6 rounded-lg">
                                <div class="text-4xl font-bold text-primary mb-2">4</div>
                                <div class="text-gray-600">国内仓库</div>
                            </div>
                        </div>
                    </div>
                    <div class="md:w-1/2">
                        <div class="relative rounded-xl overflow-hidden h-96">
                            <img src="./images/we.jpg" alt="我们的团队" class="w-full h-full object-cover object-center">
                            <div
                                class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end p-8">
                                <div class="text-white">
                                    <h4 class="text-xl font-semibold mb-2">专业物流团队</h4>
                                    <p class="text-blue-100">我们拥有经验丰富的操作团队，确保您的货物安全准时送达</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 产品服务 -->
        <section class="py-20 bg-gray-50" id="services">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">我们的产品服务</h2>
                    <div class="w-20 h-1 bg-primary mx-auto"></div>
                </div>
                <div class="grid md:grid-cols-2 gap-8">
                    <!-- 泰国陆运专线 -->
                    <div
                        class="bg-white rounded-xl shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg hover:-translate-y-1">
                        <div class="h-48 bg-blue-100 relative overflow-hidden">
                            <img src="./images/p1.jpg" alt="泰国陆运专线" class="w-full h-full object-cover object-center">
                            <div
                                class="absolute top-4 right-4 bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                                热门</div>
                        </div>
                        <div class="p-8">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                    <i class="fas fa-truck fa-icon text-primary text-xl"></i>
                                </div>
                                <h3 class="text-2xl font-semibold text-gray-800">泰国陆运专线</h3>
                            </div>
                            <p class="text-gray-600 mb-6">通过中老泰国际公路运输，提供门到门服务，适合各类普货、敏感货运输，时效稳定，价格实惠。</p>
                            <div class="mb-6">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-check-circle fa-icon text-green-500 w-5 h-5 mr-2"></i>
                                    <span class="text-gray-700">时效：5-8天</span>
                                </div>
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-check-circle fa-icon text-green-500 w-5 h-5 mr-2"></i>
                                    <span class="text-gray-700">适合：普货、敏感货</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle fa-icon text-green-500 w-5 h-5 mr-2"></i>
                                    <span class="text-gray-700">全程跟踪</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-sm text-gray-500">起运价</span>
                                    <div class="text-xl font-bold text-primary">¥???/立方</div>
                                </div>
                                <button type="button" onclick="openModal()"
                                    class="rounded-button whitespace-nowrap bg-primary hover:bg-secondary text-white px-6 py-2 transition">
                                    咨询报价
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 泰国海运专线 -->
                    <div
                        class="bg-white rounded-xl shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg hover:-translate-y-1">
                        <div class="h-48 bg-blue-100">
                            <img src="./images/p2.jpg" alt="泰国海运专线" class="w-full h-full object-cover object-center">
                        </div>
                        <div class="p-8">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                    <i class="fas fa-ship fa-icon text-primary text-xl"></i>
                                </div>
                                <h3 class="text-2xl font-semibold text-gray-800">泰国海运专线</h3>
                            </div>
                            <p class="text-gray-600 mb-6">提供整柜(FCL)和拼箱(LCL)服务，适合大宗货物运输，经济实惠，通关便捷。</p>
                            <div class="mb-6">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-check-circle fa-icon text-green-500 w-5 h-5 mr-2"></i>
                                    <span class="text-gray-700">时效：15-20天</span>
                                </div>
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-check-circle fa-icon text-green-500 w-5 h-5 mr-2"></i>
                                    <span class="text-gray-700">适合：大宗货物</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle fa-icon text-green-500 w-5 h-5 mr-2"></i>
                                    <span class="text-gray-700">报关服务：专业报关团队</span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-sm text-gray-500">起运价</span>
                                    <div class="text-xl font-bold text-primary">¥???/立方</div>
                                </div>
                                <button type="button" onclick="openModal()"
                                    class="rounded-button whitespace-nowrap bg-primary hover:bg-secondary text-white px-6 py-2 transition">
                                    咨询报价
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 服务优势 -->
        <section class="py-20 bg-white">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">我们的服务优势</h2>
                    <div class="w-20 h-1 bg-primary mx-auto"></div>
                </div>
                <div class="grid md:grid-cols-4 gap-8">
                    <div class="text-center p-6 rounded-xl hover:shadow-md transition">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-shield-alt fa-icon text-primary text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-3">安全保障</h3>
                        <p class="text-gray-600">仓到仓保险服务，专业包装防护，确保货物安全送达</p>
                    </div>
                    <div class="text-center p-6 rounded-xl hover:shadow-md transition">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-clock fa-icon text-primary text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-3">时效稳定</h3>
                        <p class="text-gray-600">固定班次发车，泰国本地配送网络，确保时效稳定可靠</p>
                    </div>
                    <div class="text-center p-6 rounded-xl hover:shadow-md transition">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-yen-sign fa-icon text-primary text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-3">价格透明</h3>
                        <p class="text-gray-600">无隐藏收费，按实际重量/体积计费，大客户享有专属优惠</p>
                    </div>
                    <div class="text-center p-6 rounded-xl hover:shadow-md transition">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-headset fa-icon text-primary text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-3">专业服务</h3>
                        <p class="text-gray-600">中泰双语客服，24小时响应，专业报关团队，解决各类跨境物流难题</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 仓库网络 -->
        <section class="py-20 bg-gray-50" id="warehouse">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">我们的仓库网络</h2>
                    <div class="w-20 h-1 bg-primary mx-auto"></div>
                </div>

                <div class="grid md:grid-cols-2 lg:grid-cols-2 gap-6">
                    <!-- 深圳沙井总仓 -->
                    <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition flex flex-col h-full">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                <i class="fas fa-warehouse fa-icon text-primary text-xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800">深圳沙井总仓</h3>
                        </div>
                        <div class="space-y-3 mb-6 flex-grow">
                            <div class="flex">
                                <i class="fas fa-map-marker-alt fa-icon text-gray-400 w-5 h-5 mt-1 mr-2"></i>
                                <p class="text-gray-600">深圳市宝安区黄埔路157号东诚易胜国际（跨越物流正门右边）</p>
                            </div>
                            <div class="flex">
                                <i class="fas fa-user fa-icon text-gray-400 w-5 h-5 mt-1 mr-2"></i>
                                <p class="text-gray-600">东诚 15637075929</p>
                            </div>
                            <div class="flex">
                                <i class="fas fa-clock fa-icon text-gray-400 w-5 h-5 mt-1 mr-2"></i>
                                <p class="text-gray-600">收货时间：<br>周一到周六：9:00-12:00 13:30-18:00</p>
                            </div>
                        </div>

                    </div>

                    <!-- 龙岗坂田仓 -->
                    <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition flex flex-col h-full">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                <i class="fas fa-warehouse fa-icon text-primary text-xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800">深圳龙岗分仓</h3>
                        </div>
                        <div class="space-y-3 mb-6 flex-grow">
                            <div class="flex">
                                <i class="fas fa-map-marker-alt fa-icon text-gray-400 w-5 h-5 mt-1 mr-2"></i>
                                <p class="text-gray-600">深圳市龙岗区坂田街道雪岗路2004号景和源1栋102东诚易胜国际</p>
                            </div>
                            <div class="flex">
                                <i class="fas fa-user fa-icon text-gray-400 w-5 h-5 mt-1 mr-2"></i>
                                <p class="text-gray-600">东诚 18124563389</p>
                            </div>
                            <div class="flex">
                                <i class="fas fa-clock fa-icon text-gray-400 w-5 h-5 mt-1 mr-2"></i>
                                <p class="text-gray-600">收货时间：
                                    <br>周一到周六：9:00-12:00 13:30-18:00
                                </p>
                            </div>
                        </div>

                    </div>

                    <!-- 广州仓 -->
                    <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition flex flex-col h-full">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                <i class="fas fa-warehouse fa-icon text-primary text-xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800">广州白云分仓</h3>
                        </div>
                        <div class="space-y-3 mb-6 flex-grow">
                            <div class="flex">
                                <i class="fas fa-map-marker-alt fa-icon text-gray-400 w-5 h-5 mt-1 mr-2"></i>
                                <p class="text-gray-600">广州市白云区滘心大道中北白云湖仓储园N16东诚易胜国际</p>
                            </div>
                            <div class="flex">
                                <i class="fas fa-user fa-icon text-gray-400 w-5 h-5 mt-1 mr-2"></i>
                                <p class="text-gray-600">东诚 19953183613</p>
                            </div>
                            <div class="flex">
                                <i class="fas fa-clock fa-icon text-gray-400 w-5 h-5 mt-1 mr-2"></i>
                                <p class="text-gray-600">收货时间：
                                    <br>周一到周六：9:00-12:00 13:30-18:00
                                </p>
                            </div>
                        </div>

                    </div>

                    <!-- 义乌仓 -->
                    <div class="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition flex flex-col h-full">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                                <i class="fas fa-warehouse fa-icon text-primary text-xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800">金华义乌分仓</h3>
                        </div>
                        <div class="space-y-3 mb-6 flex-grow">
                            <div class="flex">
                                <i class="fas fa-map-marker-alt fa-icon text-gray-400 w-5 h-5 mt-1 mr-2"></i>
                                <p class="text-gray-600">浙江省义乌市后宅街道神州路251号易家园区7号楼101东诚易胜国际</p>
                            </div>
                            <div class="flex">
                                <i class="fas fa-user fa-icon text-gray-400 w-5 h-5 mt-1 mr-2"></i>
                                <p class="text-gray-600">东诚 13345796039</p>
                            </div>
                            <div class="flex">
                                <i class="fas fa-clock fa-icon text-gray-400 w-5 h-5 mt-1 mr-2"></i>
                                <p class="text-gray-600">收货时间：<br>周一到周六：9:00-12:00 13:30-18:00</p>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </section>

        <!-- 合作客户 -->
        <section class="py-20 bg-white hidden">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">我们的合作客户</h2>
                    <div class="w-20 h-1 bg-primary mx-auto"></div>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
                    <div class="bg-gray-50 h-32 flex items-center justify-center p-6 rounded-lg">
                        <div class="text-2xl font-logo text-gray-500">客户A</div>
                    </div>
                    <div class="bg-gray-50 h-32 flex items-center justify-center p-6 rounded-lg">
                        <div class="text-2xl font-logo text-gray-500">客户B</div>
                    </div>
                    <div class="bg-gray-50 h-32 flex items-center justify-center p-6 rounded-lg">
                        <div class="text-2xl font-logo text-gray-500">客户C</div>
                    </div>
                    <div class="bg-gray-50 h-32 flex items-center justify-center p-6 rounded-lg">
                        <div class="text-2xl font-logo text-gray-500">客户D</div>
                    </div>
                    <div class="bg-gray-50 h-32 flex items-center justify-center p-6 rounded-lg">
                        <div class="text-2xl font-logo text-gray-500">客户E</div>
                    </div>
                    <div class="bg-gray-50 h-32 flex items-center justify-center p-6 rounded-lg">
                        <div class="text-2xl font-logo text-gray-500">客户F</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 新闻动态 -->
        <section class="py-20 bg-gray-50" id="news">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">新闻动态</h2>
                    <div class="w-20 h-1 bg-primary mx-auto"></div>
                </div>
                <div class="grid md:grid-cols-3 gap-8">
                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition">
                        <div class="h-48 bg-blue-100">
                            <img src="./images/kaiye.jpg" alt="新闻图片" class="w-full h-full object-cover object-center">
                        </div>
                        <div class="p-6">
                            <div class="text-sm text-gray-500 mb-2">2025-06-03</div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-3">东诚易胜国际深圳公司正式成立</h3>
                            <p class="text-gray-600 mb-4">东诚易胜国际深圳公司于2025年6月3日正式成立，标志着公司在中泰跨境物流领域迈出重要一步，将为客户提供更专业的物流服务。
                            </p>

                        </div>
                    </div>

                </div>

            </div>
        </section>


    </div>



    <!-- 联系我们 -->
    <section class="py-20 bg-primary text-white" id="contact">
        <div class="container mx-auto px-6">

            <div class="grid md:grid-cols-2 gap-12">
                <div>
                    <h3 class="text-2xl font-semibold mb-6">立即咨询您的物流需求</h3>
                    <div class="space-y-6 mb-8">
                        <div class="flex items-start">
                            <i class="fas fa-phone-alt fa-icon w-6 h-6 mt-1 mr-4"></i>
                            <div class="w-full">
                                <h4 class="font-medium mb-1">咨询热线</h4>
                                <p class="text-blue-100">18025444422</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-envelope fa-icon w-6 h-6 mt-1 mr-4"></i>
                            <div class="w-full">
                                <h4 class="font-medium mb-1">电子邮箱</h4>
                                <p class="text-blue-100"><EMAIL></p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-map-marker-alt fa-icon w-6 h-6 mt-1 mr-4"></i>
                            <div class="w-full">
                                <h4 class="font-medium mb-1">公司地址</h4>
                                <p class="text-blue-100">深圳市宝安区和秀西路68号深腾耀商务大厦308</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 微信咨询模态框 -->
    <div id="consultModal" class="modal">
        <div class="modal-content">
            <button type="button" class="modal-close" onclick="closeModal()">&times;</button>
            <div class="text-center">
                <div class="mb-6">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fab fa-weixin text-green-600 text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">关注我们 / 联系我们</h3>
                    <div class="flex items-center justify-center mb-4">
                        <div class="flex items-center bg-green-50 px-3 py-1 rounded-full">
                            <i class="fas fa-circle-check text-green-600 text-sm mr-2"></i>
                            <span class="text-green-700 text-sm font-medium">微信公众号</span>
                        </div>
                    </div>
                    <!-- <p class="text-gray-600">扫描二维码关注微信公众号</p> -->
                </div>

                <div class="bg-white p-6 rounded-xl border-2 border-gray-100 mb-2">
                    <img src="./images/qrcode.svg" alt="微信二维码" class="w-48 h-48 mx-auto">
                    <div>关注微信公众号扫一扫</div>
                </div>

                <!-- <div class="space-y-3 text-sm text-gray-600">
                    <div class="flex items-center justify-center">
                        <i class="fas fa-clock text-gray-400 mr-2"></i>
                        <span>服务时间：9:00-18:00（周一至周六）</span>
                    </div>
                    <div class="flex items-center justify-center">
                        <i class="fas fa-headset text-gray-400 mr-2"></i>
                        <span>专业客服，快速响应</span>
                    </div>
                </div> -->

                <!-- <div class=" bg-green-500 font-medium rounded-xl border-2 border-gray-100 mb-2">
                    <a href="https://work.weixin.qq.com/ca/cawcde65ba6320056e" target="_blank"
                        class="text-white flex items-center justify-center space-x-2 px-4 py-2 rounded-lg transition">
                        <i class="fas fa-user-headset"></i>
                        <span>点击立即微信咨询</span>
                    </a>
                </div> -->

                <div class="mt-6 pt-4">
                    <p class="text-xs text-gray-500">
                        官方咨询热线 <span class="font-medium text-primary">18025444422</span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动按钮 -->
    <div id="floatingButtons" class="floating-buttons">
        <button type="button" class="floating-btn back-to-top mb-2" data-tooltip="返回顶部" onclick="scrollToTop()"
            title="返回顶部">
            <i class="fas fa-arrow-up"></i>
        </button>
        <button type="button" class="floating-btn contact-us" data-tooltip="联系我们" onclick="openModal()" title="联系我们">
            <i class="fas fa-comments"></i>
        </button>
    </div>

    <script src="js/main.js?ver=20250728"></script>

    <!--<script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>-->
    <!--<script>-->
    <!--    var vConsole = new window.VConsole();-->
    <!--</script>-->
</body>

</html>