// ie10 


var server = 'https://cdn.gz.shichengguoji.com/DC/index.php?route=';

// local
if (window.location.href.indexOf('localhost') !== -1 || window.location.href.indexOf('127.0.0.1') !== -1) {
    server = 'http://localhost.ptlogin2.qq.com:6608/DcApi/index.php?route=';
}



/**
 * 打开咨询模态框
 */
function openModal() {
    var modal = document.getElementById('consultModal');
    if (modal) {
        if (modal.classList) {
            modal.classList.add('show');
        } else {
            modal.className += ' show';
        }
        document.body.style.overflow = 'hidden'; // 防止背景滚动
    }
}

// 关闭模态框
function closeModal() {
    var modal = document.getElementById('consultModal');
    if (modal) {
        if (modal.classList) {
            modal.classList.remove('show');
        } else {
            modal.className = modal.className.replace(/\bshow\b/g, '');
        }
        document.body.style.overflow = 'auto'; // 恢复滚动
    }
}

// 浮动按钮功能：平滑滚动到顶部
function scrollToTop() {
    var scrollDuration = 500; // 滚动持续时间（毫秒）
    var scrollStep = -window.scrollY / (scrollDuration / 15);
    var scrollInterval = setInterval(function () {
        if (window.scrollY != 0) {
            window.scrollBy(0, scrollStep);
        } else {
            clearInterval(scrollInterval);
        }
    }, 15);
}

// 页面路由控制
function showPage(pageId) {
    // 隐藏所有页面
    var pages = document.querySelectorAll('[pages]');
    for (var i = 0; i < pages.length; i++) {
        pages[i].style.display = 'none';
    }

    // 显示指定页面
    var targetPage = document.querySelector('[pages="' + pageId + '"]');
    if (targetPage) {
        targetPage.style.display = 'block';
    }

    // 更新 URL hash
    if (window.history && window.history.replaceState) {
        window.history.replaceState(null, null, '#' + pageId);
    } else {
        window.location.hash = pageId;
    }

    // 回到顶部
    window.scrollTo(0, 0);
}

// 初始化路由
function initRouter() {
    var url = window.location.href;
    var pageId = url.split('#')[1];

    if (pageId && document.querySelector('[pages="' + pageId + '"]')) {
        showPage(pageId);
    } else {
        showPage('index');
    }

    // 监听浏览器前进/后退按钮
    if (window.addEventListener) {
        window.addEventListener('hashchange', function () {
            var newPageId = window.location.hash.substring(1);
            if (newPageId && document.querySelector('[pages="' + newPageId + '"]')) {
                showPage(newPageId);
            } else {
                showPage('index');
            }
        });
    } else if (window.attachEvent) {
        window.attachEvent('onhashchange', function () {
            var newPageId = window.location.hash.substring(1);
            if (newPageId && document.querySelector('[pages="' + newPageId + '"]')) {
                showPage(newPageId);
            } else {
                showPage('index');
            }
        });
    }
}

// 请求派送搜索
function searchDeliveryPrice() {
    var destinationAddress = document.getElementById('destinationAddress');
    var submitButton = document.getElementById('submitButton');

    if (!destinationAddress || !destinationAddress.value.trim()) {
        showNotification('请输入目的地地址', 'warning');
        if (destinationAddress) destinationAddress.focus();
        return;
    }

    if (submitButton) submitButton.disabled = true;

    var data = JSON.stringify({
        "destination": destinationAddress.value.trim()
    });

    var xhr = new XMLHttpRequest();
    xhr.addEventListener("readystatechange", function () {
        if (this.readyState === 4) {
            try {
                var response = JSON.parse(this.responseText);
                if (response.code === 200) {
                    searchDeliveryPriceSuccess(response.data);
                } else {
                    searchDeliveryPriceError(response.msg || '未知错误');
                }
            } catch (e) {
                console.error('解析响应失败:', e);
                searchDeliveryPriceError('服务器返回的数据格式错误');
            }
            if (submitButton) submitButton.disabled = false;
            hideDeliveryLoadingState();
        }
    });

    xhr.open("POST", server + "/getPrice");
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send(data);

    showDeliveryLoadingState();
}

// 显示派送加载状态
function showDeliveryLoadingState() {
    var loadingState = document.getElementById('deliveryLoadingState');
    var results = document.getElementById('deliveryResults');
    var noResults = document.getElementById('deliveryNoResults');

    if (loadingState) loadingState.classList.remove('hidden');
    if (results) results.classList.add('hidden');
    if (noResults) noResults.classList.add('hidden');
}

// 隐藏派送加载状态
function hideDeliveryLoadingState() {
    var loadingState = document.getElementById('deliveryLoadingState');
    if (loadingState) loadingState.classList.add('hidden');
}

// 派送查询错误处理
function searchDeliveryPriceError(msg) {
    var noResults = document.getElementById('deliveryNoResults');
    var results = document.getElementById('deliveryResults');
    var noResultsTitle = document.getElementById('deliveryNoResultsTitle');

    if (noResults) noResults.classList.remove('hidden');
    if (results) results.classList.add('hidden');
    if (noResultsTitle) noResultsTitle.innerHTML = '<span class="text-red-500">查询失败：' + msg + '</span>';
}

// 派送查询成功处理
function searchDeliveryPriceSuccess(data) {
    var results = document.getElementById('deliveryResults');
    var noResults = document.getElementById('deliveryNoResults');
    var startAddress = document.getElementById('startAddress');
    var endAddress = document.getElementById('endAddress');
    var distanceValue = document.getElementById('distanceValue');
    var priceTableBody = document.getElementById('priceTableBody');

    if (results) results.classList.remove('hidden');
    if (noResults) noResults.classList.add('hidden');
    if (startAddress) startAddress.textContent = data.start_address || '暂无信息';
    if (endAddress && data.end_location) {
        var mapLink = ' <a href="https://www.bing.com/maps?osid=8&cp=' + data.end_location.lat + '~' + data.end_location.lng + '&lvl=16&v=2&sV=2&form=S00027" target="_blank" class="text-blue-500">查看bing地图</a>';
        var googleMapLink = ' <a href="https://www.google.com/maps/place/' + data.end_location.lat + ',' + data.end_location.lng + '" target="_blank" class="text-blue-500">查看google地图</a>';
        endAddress.innerHTML = (data.end_address || '暂无信息') + mapLink + ' ' + googleMapLink;
    }
    if (distanceValue) distanceValue.textContent = data.distance ? data.distance.text : '暂无信息';
    if (priceTableBody && data.price) {
        priceTableBody.innerHTML = '';
        var priceData = [
            { type: '4W', weight: '1500KG', priceCNY: data.price['4W'], description: '长2.3米、宽1.5米、高2.1米、支持6CBM、最大装载重量1500KG' },
            { type: '4WJ', weight: '2400KG', priceCNY: data.price['4WJ'], description: '长3米、宽1.75米、高2米、支持10CBM、最大装载重量2400KG' },
            { type: '6W7.2', weight: '6300KG', priceCNY: data.price['6W7.2'], description: '长7.2米、宽2.3米、高2.5米、支持39CBM、最大装载重量6300KG' }
        ];

        for (var i = 0; i < priceData.length; i++) {
            var item = priceData[i];
            if (item.priceCNY) {
                var row = document.createElement('tr');
                item.priceCNY = Math.round(item.priceCNY);
                row.innerHTML =
                    '<td><strong>' + item.type + '</strong><br><small class="text-gray-500">' + item.description + '</small></td>' +
                    '<td>' + item.weight + '</td>' +
                    '<td>' + (data.distance ? data.distance.text : '暂无信息') + '</td>' +
                    '<td><span class="delivery-price-highlight">¥' + item.priceCNY + '</span></td>'
                priceTableBody.appendChild(row);
            }
        }
    }


    // 处理 priceTableBodyLCL 费用
    // 根据举例计算 派送费多少/CBM
    let priceTableBodyLCL = document.getElementById('priceTableBodyLCL');
    if (priceTableBodyLCL && data.distance) {
        priceTableBodyLCL.innerHTML = '';
        var distance = parseFloat(data.distance.text);
        var pricePerCBM = 0;
        var pricePerMSG = "";
        if (distance <= 80) {
            pricePerCBM = 0;
            pricePerMSG = "【超大件加收¥50/CBM】";
        } else if (distance <= 100) {
            pricePerCBM = 100;
            pricePerMSG = "";
        } else if (distance <= 200) {
            pricePerCBM = 150;
            pricePerMSG = "";
        } else if (distance <= 500) {
            pricePerCBM = 200;
            pricePerMSG = "";
        } else if (distance > 500) {
            pricePerCBM = 250;
            pricePerMSG = "";
        } else {
            pricePerCBM = 999;
            pricePerMSG = "【请联系客服询价】";
        }

        var row = document.createElement('tr');
        row.innerHTML =
            '<td>拼车派送（最低消费 1CBM）</td>' +
            '<td>' + distance + 'km</td>' +
            '<td><span class="delivery-price-highlight">¥' + pricePerCBM + '/CBM 立方 ' + pricePerMSG + '</span></td>'
        priceTableBodyLCL.appendChild(row);
        // priceTableBodyAppend
        var priceTableBodyAppend = document.getElementById('priceTableBodyAppend');
        if (priceTableBodyAppend) {
            priceTableBodyAppend.innerHTML = '';
            var appendData = [
                { type: '单件实重≥200-299KG', price: '单件收取100RMB/件，最高收费200RMB/票' },
                { type: '单件实重≥300-1499KG', price: '单件收取150RMB/件，最高收费300RMB/票' },
                { type: '单件实重≥1500-2499KG', price: '单件收取300RMB/件，最高收费600RMB/票' },
                { type: '单件实重≥2500-5000KG', price: '单票收取3500RMB吊车装卸费' },
                { type: '单件实重≥5001kg', price: '请联系对接业务单询' },
                { type: '单件长度≥200-299CM', price: '单件收取150RMB/件，最高收费300RMB/票' },
                { type: '单件长度≥300-399CM', price: '单件收取300RMB/件，最高收费600RMB/票' },
                { type: '单件长度≥400-499CM', price: '单票收取3500RMB吊车装卸费' },
                { type: '单件长度≥500CM', price: '请联系对接业务单询' },
            ];
            for (var i = 0; i < appendData.length; i++) {
                var item = appendData[i];
                var row = document.createElement('tr');
                row.innerHTML =
                    '<td>' + item.type + '</td>' +
                    '<td colspan="3">' + item.price + '</td>'
                    priceTableBodyAppend.appendChild(row);
            }
        }

    } else {
        // 隐藏priceTableBodyLCL
        priceTableBodyLCL.style = 'display: none;';
    }





}








// 清空派送输入
function clearDeliveryInput() {
    var destinationAddress = document.getElementById('destinationAddress');
    var results = document.getElementById('deliveryResults');
    var noResults = document.getElementById('deliveryNoResults');

    if (destinationAddress) destinationAddress.value = '';
    if (results) results.classList.add('hidden');
    if (noResults) noResults.classList.add('hidden');
}

// 表单验证增强
function validateTrackingNumbers(numbers) {
    var errors = [];
    var validNumbers = [];

    for (var index = 0; index < numbers.length; index++) {
        var num = numbers[index];
        var trimmed = num.trim();
        if (trimmed.length === 0) {
            continue; // 跳过空行
        }

        if (trimmed.length < 3) {
            errors.push('第' + (index + 1) + '行：运单号太短（至少3个字符）');
        } else if (trimmed.length > 50) {
            errors.push('第' + (index + 1) + '行：运单号太长（最多50个字符）');
        } else if (!/^[A-Za-z0-9\-_]+$/.test(trimmed)) {
            errors.push('第' + (index + 1) + '行：运单号包含无效字符');
        } else {
            validNumbers.push(trimmed);
        }
    }

    return {
        valid: errors.length === 0,
        errors: errors,
        validNumbers: validNumbers
    };
}

// 查询轨迹
function searchTrackingImproved() {
    var trackingNumbers = document.getElementById('trackingNumbers');
    if (!trackingNumbers || !trackingNumbers.value.trim()) {
        showNotification('请输入运单号码', 'warning');
        if (trackingNumbers) trackingNumbers.focus();
        return;
    }

    // 处理多个运单号（按行分割）
    var numbers = trackingNumbers.value.trim().split('\n');
    var validation = validateTrackingNumbers(numbers);

    if (!validation.valid) {
        showNotification('输入格式错误：\n' + validation.errors.join('\n'), 'error');
        return;
    }

    if (validation.validNumbers.length === 0) {
        showNotification('请输入有效的运单号码', 'warning');
        return;
    }

    if (validation.validNumbers.length > 10) {
        showNotification('一次最多查询10个运单号', 'warning');
        return;
    }

    // 更新运单计数器
    var trackingCounter = document.getElementById('trackingCounter');
    if (trackingCounter) trackingCounter.textContent = validation.validNumbers.length;

    // 拼接参数
    var connoteParam = validation.validNumbers.join(',');

    // 显示加载状态
    trackingShowHide('loadingShow');

    // 创建占位行
    var resultsContainer = document.getElementById('resultsContainer');
    if (resultsContainer) resultsContainer.innerHTML = '';
    for (var i = 0; i < validation.validNumbers.length; i++) {
        var row = document.createElement('tr');
        row.id = validation.validNumbers[i];
        row.innerHTML = '<td>' + validation.validNumbers[i] + '</td><td>加载中</td>';
        if (resultsContainer) resultsContainer.appendChild(row);
    }

    // 请求新接口
    var xhr = new XMLHttpRequest();
    xhr.open('GET', server + '/getTracks&connote=' + encodeURIComponent(connoteParam), true);
    xhr.setRequestHeader('Accept', 'application/json, text/javascript, */*; q=0.01');
    xhr.timeout = 8000;

    xhr.onreadystatechange = function () {
        if (xhr.readyState === 4) {
            trackingShowHide('loadingHide');
            if (xhr.status === 200) {
                var responseText = xhr.responseText;
                if (typeof responseText === 'string' && responseText.length > 0) {
                    try {
                        var response = JSON.parse(responseText);
                        if (response && response.code === 200 && response.data) {
                            trackingSuccessV2(response.data, validation.validNumbers);
                        } else {
                            showNotification('查询失败: ' + (response.msg || '未知错误'), 'error');
                            trackingShowHide('noResultsShow');
                        }
                    } catch (e) {
                        console.error('解析响应失败:', e);
                        showNotification('解析结果失败，请稍后重试', 'error');
                        trackingShowHide('noResultsShow');
                    }
                } else {
                    showNotification('解析结果失败，请稍后重试', 'error');
                    trackingShowHide('noResultsShow');
                }
            } else {
                showNotification('请求失败，状态码: ' + xhr.status, 'error');
                trackingShowHide('noResultsShow');
            }
        }
    };

    xhr.onerror = function () {
        trackingShowHide('loadingHide');
        showNotification('网络请求失败，请检查网络连接', 'error');
    };

    xhr.send();
}

// 新接口轨迹查询成功处理
function trackingSuccessV2(data, trackingNumbers) {
    var resultsContainer = document.getElementById('resultsContainer');
    var trackingResults = document.getElementById('trackingResults');
    var resultsCount = document.getElementById('resultsCount');
    var foundCount = 0;

    if (resultsContainer) resultsContainer.innerHTML = '';

    for (var i = 0; i < trackingNumbers.length; i++) {
        var num = trackingNumbers[i];
        var row = document.createElement('tr');
        row.id = num;
        if (data[num] && data[num].tracks && Array.isArray(data[num].tracks) && data[num].tracks.length > 0) {
            foundCount++;
            var tracksHtml = '';
            for (var j = 0; j < data[num].tracks.length; j++) {
                var t = data[num].tracks[j];
                // 使用等宽字体显示时间
                tracksHtml += '<div><span class="text-gray-500" style="font-family: monospace, \'SFMono-Regular\', Consolas, \'Liberation Mono\', Menlo, Courier, monospace;">' + (t.date || '') + '</span> <span>' + (t.info || '') + '</span></div>';
            }
            row.innerHTML = '<td>' + num + '</td>' +
                '<td colspan="5">' + tracksHtml + '</td>';
        } else if (data[num] && data[num].msg) {
            row.innerHTML = '<td>' + num + '</td>' +
                '<td colspan="5" class="text-red-500">' + data[num].msg + '</td>';
        } else {
            row.innerHTML = '<td>' + num + '</td>' +
                '<td colspan="5" class="text-red-500">未找到匹配的物流信息</td>';
        }
        if (resultsContainer) resultsContainer.appendChild(row);
    }

    if (trackingResults) trackingResults.classList.remove('hidden');
    if (resultsCount) resultsCount.textContent = '找到 ' + foundCount + ' 条有轨迹的单号';
    if (foundCount === 0) {
        showNotification('未查询到任何有效轨迹，请检查单号', 'warning');
        trackingShowHide('noResultsShow');
    }
}

// 控制加载状态显示/隐藏
function trackingShowHide(type) {
    var loadingState = document.getElementById('loadingState');
    var trackingResults = document.getElementById('trackingResults');
    var noResults = document.getElementById('noResults');
    var tableToolbar = document.getElementById('tableToolbar');
    var statusSummary = document.getElementById('statusSummary');

    if (type === 'loadingShow') {
        if (loadingState) loadingState.classList.remove('hidden');
        if (trackingResults) trackingResults.classList.add('hidden');
        if (noResults) noResults.classList.add('hidden');
    } else if (type === 'loadingHide') {
        if (loadingState) loadingState.classList.add('hidden');
    } else if (type === 'noResultsShow') {
        if (noResults) noResults.classList.remove('hidden');
        if (trackingResults) trackingResults.classList.add('hidden');
        if (tableToolbar) tableToolbar.classList.add('hidden');
        if (statusSummary) statusSummary.classList.add('hidden');
    }
}

// 清空轨迹输入
function clearTrackingInput() {
    var trackingNumbers = document.getElementById('trackingNumbers');
    var trackingCounter = document.getElementById('trackingCounter');
    var trackingResults = document.getElementById('trackingResults');

    if (trackingNumbers) trackingNumbers.value = '';
    if (trackingCounter) trackingCounter.textContent = '0';
    if (trackingResults) trackingResults.classList.add('hidden');
}


// 移动端菜单功能
const mobileMenuButton = document.getElementById('mobileMenuButton');
const mobileMenu = document.getElementById('mobileMenu');
const menuIcon = document.getElementById('menuIcon');

function toggleMobileMenu() {
    mobileMenu.classList.toggle('hidden');
    // 切换菜单图标
    if (mobileMenu.classList.contains('hidden')) {
        menuIcon.classList.remove('fa-times');
        menuIcon.classList.add('fa-bars');
    } else {
        menuIcon.classList.remove('fa-bars');
        menuIcon.classList.add('fa-times');
    }
}

function closeMobileMenu() {
    mobileMenu.classList.add('hidden');
    // 恢复默认菜单图标
    menuIcon.classList.remove('fa-times');
    menuIcon.classList.add('fa-bars');
}

mobileMenuButton.addEventListener('click', toggleMobileMenu);


// 监听移动菜单中的链接点击事件，点击后收起菜单
document.querySelectorAll('#mobileMenu a').forEach(link => {
    link.addEventListener('click', closeMobileMenu);
});




// searchItems

function searchItems() {
    var itemKeyword = document.getElementById('itemKeyword');
    if (!itemKeyword || !itemKeyword.value.trim()) {
        showNotification('请输入产品名称', 'warning');
        if (itemKeyword) itemKeyword.focus();
        return;
    }

    var data = JSON.stringify({
        "mode": "read",
        "data": {
            "search": itemKeyword.value.trim(),
            "page": 1,
            "limit": 20
        }
    });

    var xhr = new XMLHttpRequest();
    xhr.addEventListener("readystatechange", function () {
        if (this.readyState === 4) {
            try {
                var response = JSON.parse(this.responseText);
                if (response.code === 200) {
                    searchItemsSuccess(response.data);
                } else {
                    searchItemsError(response.msg || '未知错误');
                }
            } catch (e) {
                console.error('解析响应失败:', e);
                searchItemsError('服务器返回的数据格式错误');
            }
        }
    });

    xhr.open("POST", server + "items");
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send(data);

    showItemsLoadingState();
}

// 展示数据
function searchItemsSuccess(response) {
    var resultsContainer = document.getElementById('itemsContainer');
    var itemsResults = document.getElementById('itemsResults');
    var itemsCount = document.getElementById('itemsCount');
    var itemsNoResults = document.getElementById('itemsNoResults');
    var data = response.list || [];

    if (data.length === 0) {
        // 显示无结果提示
        if (itemsNoResults) itemsNoResults.classList.remove('hidden');
        if (itemsResults) itemsResults.classList.add('hidden');
        var noResultsTitle = document.querySelector('.items-empty-title');
        if (noResultsTitle) noResultsTitle.textContent = '此产品请与管理员单询';
        hideItemsLoadingState();
        return;
    }

    if (itemsResults) itemsResults.classList.remove('hidden');
    if (itemsNoResults) itemsNoResults.classList.add('hidden');
    if (itemsCount) itemsCount.textContent = '找到 ' + response.total + ' 条记录';
    if (resultsContainer) resultsContainer.innerHTML = '';

    // 遍历数据创建表格行
    for (var i = 0; i < data.length; i++) {
        var item = data[i];
        var row = document.createElement('tr');
        row.innerHTML =
            '<td>' + (item.category || '-') + '</td>' +
            '<td>' + (item.name || '-') + '</td>' +
            '<td>' + (item.search_keywords || '-') + '</td>' +
            '<td>' + (item.hscode || '-') + '</td>' +
            '<td>' + (item.remarks || '-') + '</td>';
        resultsContainer.appendChild(row);
    }
    // 隐藏加载状态
    hideItemsLoadingState();
}

function clearItemsInput() {
    var itemKeyword = document.getElementById('itemKeyword');
    if (itemKeyword) itemKeyword.value = '';
}


function showItemsLoadingState() {
    var itemsLoadingState = document.getElementById('itemsLoadingState');
    var itemsResults = document.getElementById('itemsResults');
    var itemsNoResults = document.getElementById('itemsNoResults');

    if (itemsLoadingState) itemsLoadingState.classList.remove('hidden');
    if (itemsResults) itemsResults.classList.add('hidden');
    if (itemsNoResults) itemsNoResults.classList.add('hidden');
}

function hideItemsLoadingState() {
    var itemsLoadingState = document.getElementById('itemsLoadingState');
    if (itemsLoadingState) itemsLoadingState.classList.add('hidden');
}








// 通知系统
function showNotification(message, type) {
    let typeList = {
        "success": 'check-circle',
        "error": 'exclamation-circle',
        "warning": 'exclamation-triangle',
        "info": 'info-circle',
    }

    // 创建通知元素
    var notification = document.createElement('div');
    notification.className = 'tracking-notification tracking-notification-' + type;
    notification.innerHTML =
        '<div class="tracking-notification-content">' +
        '<i class="fas fa-' + typeList[type] + ' mr-2"></i>' +
        '<span>' + message + '</span>' +
        '</div>';

    // 添加到页面
    document.body.appendChild(notification);

    // 显示动画
    setTimeout(function () {
        notification.classList.add('show');
    }, 100);

    // 自动隐藏
    setTimeout(function () {
        notification.classList.remove('show');
        setTimeout(function () {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}
















// 控制浮动按钮显示/隐藏及点击事件
function toggleFloatingButtons() {
    var floatingButtons = document.getElementById('floatingButtons');
    var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
    var windowHeight = window.innerHeight || document.documentElement.clientHeight;

    // 当滚动超过一个屏幕高度时显示按钮
    if (scrollTop > windowHeight) {
        floatingButtons.style.display = 'block';
        setTimeout(function () {
            if (floatingButtons.classList) {
                floatingButtons.classList.add('show');
            } else {
                floatingButtons.className += ' show';
            }
        }, 100); // 添加淡入动画延迟
    } else {
        if (floatingButtons.classList) {
            floatingButtons.classList.remove('show');
        } else {
            floatingButtons.className = floatingButtons.className.replace(/\bshow\b/g, '');
        }
        // 动画结束后隐藏元素
        setTimeout(function () {
            if (!floatingButtons.classList.contains('show')) {
                floatingButtons.style.display = 'none';
            }
        }, 300);
    }
}

// 添加滚动事件监听
window.addEventListener('scroll', function () {
    toggleFloatingButtons();
}, { passive: true });


// DOM 加载完成后初始化
window.onload = function () {

    initRouter();

};

