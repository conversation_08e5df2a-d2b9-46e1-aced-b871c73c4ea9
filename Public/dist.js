const fs = require('fs');
const path = require('path');
const UglifyJS = require('uglify-js');
const postcss = require('postcss');
const autoprefixer = require('autoprefixer');
const cssnano = require('cssnano');
const cheerio = require('cheerio');
const htmlMinifier = require('html-minifier');

// 处理 JavaScript 文件
async function processJS(inputPath) {
    console.log('Processing JS file:', inputPath);
    const code = fs.readFileSync(inputPath, 'utf8');
    const result = UglifyJS.minify(code, {
        compress: true,
        mangle: true
    });

    if (result.error) {
        console.error('Error processing JS:', result.error);
        return;
    }

    fs.writeFileSync(inputPath, result.code, 'utf8');
    console.log('JS file processed successfully');
}

// 处理 CSS 文件
async function processCSS(inputPath) {
    console.log('Processing CSS file:', inputPath);
    const css = fs.readFileSync(inputPath, 'utf8');
    
    try {
        const result = await postcss([
            autoprefixer,
            cssnano
        ]).process(css, { from: inputPath });

        fs.writeFileSync(inputPath, result.css, 'utf8');
        console.log('CSS file processed successfully');
    } catch (error) {
        console.error('Error processing CSS:', error);
    }
}

// 压缩 HTML 文件
async function minifyHTML(htmlPath) {
    console.log('Minifying HTML file:', htmlPath);
    const html = fs.readFileSync(htmlPath, 'utf8');
    
    const minifiedHtml = htmlMinifier.minify(html, {
        collapseWhitespace: true,
        removeComments: true,
        minifyCSS: true,
        minifyJS: true,
        removeEmptyAttributes: true,
        removeRedundantAttributes: true,
        removeScriptTypeAttributes: true,
        removeStyleLinkTypeAttributes: true,
        useShortDoctype: true
    });

    fs.writeFileSync(htmlPath, minifiedHtml, 'utf8');
    console.log('HTML file minified successfully');
}

// 更新 HTML 文件中的版本号
function updateHTMLVersions(htmlPath) {
    console.log('Updating HTML versions:', htmlPath);
    const html = fs.readFileSync(htmlPath, 'utf8');
    const $ = cheerio.load(html);
    const timestamp = new Date().getTime();

    // 更新 link 标签的 href
    $('link').each((i, elem) => {
        let href = $(elem).attr('href');
        if (href) {
            if (href.includes('?ver=')) {
                href = href.replace(/\?ver=\d+/, `?ver=${timestamp}`);
            } else {
                href = `${href}?ver=${timestamp}`;
            }
            $(elem).attr('href', href);
        }
    });

    // 更新 script 标签的 src
    $('script').each((i, elem) => {
        let src = $(elem).attr('src');
        if (src) {
            if (src.includes('?ver=')) {
                src = src.replace(/\?ver=\d+/, `?ver=${timestamp}`);
            } else {
                src = `${src}?ver=${timestamp}`;
            }
            $(elem).attr('src', src);
        }
    });

    fs.writeFileSync(htmlPath, $.html(), 'utf8');
    console.log('HTML versions updated successfully');
}

// 主函数
async function main() {
    try {
        // 处理 JS 文件
        await processJS('./static/js/js.js');

        // 处理 CSS 文件
        await processCSS('./static/css/main.css');

        // 压缩 HTML 文件
        await minifyHTML('./static/index.html');

        // 更新 HTML 文件版本号
        updateHTMLVersions('./static/index.html');

        console.log('所有文件处理完成！');
    } catch (error) {
        console.error('处理过程中发生错误:', error);
    }
}

// 运行主函数
main(); 