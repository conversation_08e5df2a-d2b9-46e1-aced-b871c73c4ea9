npx terser ./static/js/js.js  -o ./static/js/js.js



npx postcss ./static/main.css -o ./static/main.min.css

npx terser jquery.i18n.properties.js -o jquery.i18n.properties.min.js



展示搜索结果

http://127.0.0.1:5501/static/?language=en&connote=AMQ6408261,AMQ6408181#track

http://127.0.0.1:5501/static/?language=zh&searchService=2000#postcode#track


# 文件处理脚本
这个脚本用于处理 JS 和 CSS 文件，包括兼容性处理和压缩。
## 依赖

你需要安装以下依赖：
- `terser`
- `clean-css-cli`
- `postcss`
- `autoprefixer`

你可以使用以下命令来安装这些依赖：
```sh
npm install terser clean-css-cli postcss autoprefixer
node processFiles.js
```




我已经创建了完整的构建脚本，这个脚本具有以下功能：
JS 文件处理：
使用 UglifyJS 进行压缩和混淆
处理 ./static/js/js.js 文件
CSS 文件处理：
使用 PostCSS 处理
应用 Autoprefixer 添加浏览器前缀
使用 cssnano 进行压缩
处理 ./static/main.css 文件
HTML 文件处理：
更新所有 link 和 script 标签的版本号
为没有版本号的链接添加版本号
处理 ./static/index.html 文件
使用方法：
首先安装依赖：
npm install
运行构建脚本：
npm run build
脚本会自动处理所有文件，并在控制台输出处理进度。如果发生任何错误，都会在控制台显示详细信息。
注意事项：
确保所有目标文件路径正确（./static/js/js.js、./static/main.css 和 ./static/index.html）
脚本会直接覆盖原文件，建议在运行前备份重要文件
所有文件的处理都是异步的，但会按顺序执行
如果你需要修改任何配置或文件路径，可以直接在 dist.js 中相应的函数中进行调整。