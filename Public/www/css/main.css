
/* 基础样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: '<PERSON>o', Arial, sans-serif;
    margin: 0;
    padding: 0;
    padding-bottom: 60px; /* 为底部导航留出空间 */
}

/* Logo 样式 */
.logo {
    font-size: 18px;
    font-weight: bold;
    color: #2196F3;
}

/* 语言切换按钮 */
.lang-btn {
    cursor: pointer;
    background-color: #F3F4F6;
    border: none;
    border-radius: 4px;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.lang-btn.active {
    background-color: #2196F3;
    color: white;
}

.lang-btn:hover {
    background-color: #1976D2;
    color: white;
}

/* 导航菜单 */
.nav-item {
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.nav-item.active {
    border-bottom-color: #2196F3;
    color: #2196F3;
}

.nav-item:hover {
    background-color: #f5f5f5;
}

/* 运单查询标签 */
.track-tab {
    transition: all 0.3s ease;
    border: none;
    border-radius: 0;
}

.track-tab.active {
    background-color: #2196F3 !important;
    color: white !important;
}

/* 搜索按钮 */
#searchBtn {
    height: 40px;
    border: none;
    transition: all 0.3s ease;
}

#searchBtn:hover {
    background-color: #1976D2;
}

/* 快捷服务图标 */
.service-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    transition: transform 0.3s ease;
    cursor: pointer;
}

.service-icon:hover {
    transform: scale(1.1);
}

/* 服务卡片 */
.service-card {
    background: linear-gradient(135deg, #FF6B35 0%, #F97A4A 100%);
    border-radius: 8px;
    color: white;
    position: relative;
    overflow: hidden;
}

.truck-image {
    max-width: 80px;
    height: auto;
}

/* 移动端底部导航 */
.mobile-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-top: 1px solid #e0e0e0;
}

.mobile-footer .w3-bar-item {
    flex: 1;
    padding: 8px 4px;
}

/* 响应式设计 */
@media (max-width: 600px) {
    /* 头部调整 */
    header .w3-bar-item {
        padding: 8px;
    }

    .logo {
        font-size: 16px;
    }

    .lang-btn {
        font-size: 12px;
        padding: 4px 8px;
    }

    /* 导航菜单移动端优化 */
    nav .w3-bar-item {
        font-size: 14px;
        padding: 12px 8px;
    }

    /* 运单查询区域 */
    .track-tab {
        font-size: 12px;
        padding: 8px 4px;
    }

    #trackingInput {
        font-size: 14px;
        height: 40px;
    }

    /* 快捷服务 */
    .service-icon {
        width: 50px;
        height: 50px;
    }

    .service-icon span {
        font-size: 20px;
    }

    /* 服务卡片 */
    .service-card h4 {
        font-size: 16px;
        margin: 0 0 8px 0;
    }

    .service-card p {
        font-size: 12px;
        margin: 0;
    }

    .truck-image {
        max-width: 60px;
    }
}

/* 平板设备 */
@media (min-width: 601px) and (max-width: 992px) {
    .mobile-footer {
        display: none;
    }
}

/* 桌面设备 */
@media (min-width: 993px) {
    .mobile-footer {
        display: none;
    }

    body {
        padding-bottom: 0;
    }
}

/* IE10/IE11 兼容性 */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    .service-icon {
        display: block;
        text-align: center;
        line-height: 60px;
    }

    .service-card {
        background: #FF6B35;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}