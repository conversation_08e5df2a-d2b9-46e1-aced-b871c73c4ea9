// 等待DOM加载完成
$(document).ready(function() {
    // 初始化应用
    initApp();
});

// 应用初始化
function initApp() {
    // 绑定事件监听器
    bindEventListeners();

    // 初始化语言
    initLanguage();

    // 初始化动画
    initAnimations();

    // 检查IE兼容性
    checkIECompatibility();
}

// 绑定事件监听器
function bindEventListeners() {
    // 语言切换
    $('.lang-btn').on('click', function() {
        var lang = $(this).data('lang');
        switchLanguage(lang);

        // 更新活跃状态
        $('.lang-btn').removeClass('active');
        $(this).addClass('active');
    });

    // 导航菜单切换
    $('.nav-item').on('click', function(e) {
        e.preventDefault();
        var section = $(this).data('section');

        // 更新活跃状态
        $('.nav-item').removeClass('active');
        $(this).addClass('active');

        // 切换内容区域
        switchSection(section);
    });

    // 运单查询标签切换
    $('.track-tab').on('click', function() {
        var tab = $(this).data('tab');

        // 更新标签状态
        $('.track-tab').removeClass('active').removeClass('w3-blue').addClass('w3-light-grey');
        $(this).addClass('active').addClass('w3-blue').removeClass('w3-light-grey');

        // 更新输入框占位符
        updateTrackingPlaceholder(tab);
    });

    // 搜索按钮点击
    $('#searchBtn').on('click', function() {
        performSearch();
    });

    // 输入框回车搜索
    $('#trackingInput').on('keypress', function(e) {
        if (e.which === 13) { // Enter键
            performSearch();
        }
    });

    // 快捷服务点击
    $('.service-icon').on('click', function() {
        var parent = $(this).parent();
        var serviceType = parent.find('p').text();
        handleServiceClick(serviceType);
    });

    // 移动端底部导航
    $('.mobile-footer .w3-bar-item').on('click', function(e) {
        e.preventDefault();

        // 更新活跃状态
        $('.mobile-footer .w3-bar-item').removeClass('w3-blue').addClass('w3-white');
        $(this).removeClass('w3-white').addClass('w3-blue');

        // 处理导航
        var navText = $(this).find('.w3-tiny').text();
        handleMobileNavigation(navText);
    });
}

// 语言切换功能
function switchLanguage(lang) {
    var translations = {
        'zh': {
            'home': '首页',
            'services': '服务介绍',
            'about': '关于我们',
            'contact': '联系我们',
            'track': '运单轨迹',
            'address': '收货地址',
            'goods': '货运品名',
            'placeholder': '请输入运单号',
            'quickService': '快捷服务',
            'priceQuery': '价格查询',
            'onlineService': '在线客服',
            'networkQuery': '网点查询',
            'appointment': '预约寄件',
            'ourService': '我们的服务',
            'thailandLine': '泰国专线',
            'thailandDesc': '3-5工作日送达，全程可追踪',
            'airExpress': '空运快递',
            'airDesc': '1-3工作日送达',
            'seaService': '海运服务',
            'seaDesc': '7-15工作日送达'
        },
        'th': {
            'home': 'หน้าแรก',
            'services': 'บริการ',
            'about': 'เกี่ยวกับเรา',
            'contact': 'ติดต่อเรา',
            'track': 'ติดตามพัสดุ',
            'address': 'ที่อยู่รับสินค้า',
            'goods': 'ชื่อสินค้า',
            'placeholder': 'กรุณาใส่หมายเลขติดตาม',
            'quickService': 'บริการด่วน',
            'priceQuery': 'สอบถามราคา',
            'onlineService': 'บริการออนไลน์',
            'networkQuery': 'สอบถามสาขา',
            'appointment': 'นัดหมายส่งของ',
            'ourService': 'บริการของเรา',
            'thailandLine': 'สายไทย',
            'thailandDesc': 'ส่งถึงใน 3-5 วันทำการ ติดตามได้',
            'airExpress': 'ขนส่งทางอากาศ',
            'airDesc': 'ส่งถึงใน 1-3 วันทำการ',
            'seaService': 'ขนส่งทางเรือ',
            'seaDesc': 'ส่งถึงใน 7-15 วันทำการ'
        },
        'en': {
            'home': 'Home',
            'services': 'Services',
            'about': 'About Us',
            'contact': 'Contact',
            'track': 'Track Package',
            'address': 'Delivery Address',
            'goods': 'Goods Name',
            'placeholder': 'Enter tracking number',
            'quickService': 'Quick Service',
            'priceQuery': 'Price Query',
            'onlineService': 'Online Service',
            'networkQuery': 'Network Query',
            'appointment': 'Schedule Pickup',
            'ourService': 'Our Services',
            'thailandLine': 'Thailand Line',
            'thailandDesc': '3-5 working days delivery, fully trackable',
            'airExpress': 'Air Express',
            'airDesc': '1-3 working days delivery',
            'seaService': 'Sea Service',
            'seaDesc': '7-15 working days delivery'
        }
    };

    var currentLang = translations[lang] || translations['zh'];

    // 更新页面文本
    updatePageText(currentLang);

    // 保存语言设置
    localStorage.setItem('selectedLanguage', lang);
}

// 更新页面文本
function updatePageText(translations) {
    // 更新导航
    $('[data-section="home"]').text(translations.home);
    $('[data-section="services"]').text(translations.services);
    $('[data-section="about"]').text(translations.about);
    $('[data-section="contact"]').text(translations.contact);

    // 更新查询标签
    $('[data-tab="track"]').text(translations.track);
    $('[data-tab="address"]').text(translations.address);
    $('[data-tab="goods"]').text(translations.goods);

    // 更新输入框
    $('#trackingInput').attr('placeholder', translations.placeholder);

    // 更新服务文本
    $('h3').first().text(translations.quickService);
    $('h3').last().text(translations.ourService);
}

// 初始化语言
function initLanguage() {
    var savedLang = localStorage.getItem('selectedLanguage') || 'zh';
    $('.lang-btn[data-lang="' + savedLang + '"]').click();
}

// 更新运单查询占位符
function updateTrackingPlaceholder(tab) {
    var placeholders = {
        'track': '请输入运单号',
        'address': '请输入收货地址',
        'goods': '请输入货运品名'
    };

    $('#trackingInput').attr('placeholder', placeholders[tab] || placeholders['track']);
}

// 执行搜索
function performSearch() {
    var query = $('#trackingInput').val().trim();

    if (!query) {
        showMessage('请输入查询内容', 'warning');
        return;
    }

    // 显示加载状态
    $('#searchBtn').addClass('loading');

    // 模拟搜索请求
    setTimeout(function() {
        $('#searchBtn').removeClass('loading');
        showMessage('查询功能开发中...', 'info');
    }, 1000);
}

// 处理快捷服务点击
function handleServiceClick(serviceType) {
    showMessage('正在打开 ' + serviceType + '...', 'info');

    // 这里可以添加具体的服务逻辑
    switch(serviceType) {
        case '价格查询':
            // 打开价格查询页面
            break;
        case '在线客服':
            // 打开客服聊天
            break;
        case '网点查询':
            // 打开网点查询
            break;
        case '预约寄件':
            // 打开预约页面
            break;
    }
}

// 处理移动端导航
function handleMobileNavigation(navText) {
    switch(navText) {
        case '首页':
            $('[data-section="home"]').click();
            break;
        case '查询':
            $('#trackingInput').focus();
            break;
        case '客服':
            showMessage('正在连接客服...', 'info');
            break;
        case '我的':
            showMessage('个人中心开发中...', 'info');
            break;
    }
}

// 切换内容区域
function switchSection(section) {
    // 这里可以实现不同页面内容的切换
    console.log('切换到:', section);
}

// 显示消息提示
function showMessage(message, type) {
    type = type || 'info';

    var alertClass = 'w3-pale-blue';
    switch(type) {
        case 'success':
            alertClass = 'w3-pale-green';
            break;
        case 'warning':
            alertClass = 'w3-pale-yellow';
            break;
        case 'error':
            alertClass = 'w3-pale-red';
            break;
    }

    var alertHtml = '<div class="w3-panel ' + alertClass + ' w3-border w3-round message-alert">' +
                   '<span onclick="this.parentElement.style.display=\'none\'" ' +
                   'class="w3-button w3-large w3-display-topright">&times;</span>' +
                   '<p>' + message + '</p></div>';

    // 移除现有消息
    $('.message-alert').remove();

    // 添加新消息
    $('main').prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(function() {
        $('.message-alert').fadeOut(function() {
            $(this).remove();
        });
    }, 3000);
}

// 初始化动画
function initAnimations() {
    // 为页面元素添加淡入动画
    $('section').addClass('fade-in');

    // 滚动动画
    $(window).scroll(function() {
        var scrollTop = $(this).scrollTop();

        // 可以添加滚动相关的动画效果
        if (scrollTop > 100) {
            $('header').addClass('w3-card-4');
        } else {
            $('header').removeClass('w3-card-4');
        }
    });
}

// 检查IE兼容性
function checkIECompatibility() {
    // 检测IE浏览器
    var isIE = /*@cc_on!@*/false || !!document.documentMode;

    if (isIE) {
        // 为IE添加特殊处理
        $('body').addClass('ie-browser');

        // 禁用一些现代CSS特性
        $('.service-icon').css('border-radius', '50%');

        console.log('IE浏览器兼容模式已启用');
    }
}

// 工具函数：防抖
function debounce(func, wait) {
    var timeout;
    return function executedFunction() {
        var context = this;
        var args = arguments;

        var later = function() {
            timeout = null;
            func.apply(context, args);
        };

        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 工具函数：节流
function throttle(func, limit) {
    var inThrottle;
    return function() {
        var args = arguments;
        var context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(function() {
                inThrottle = false;
            }, limit);
        }
    };
}