<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>泰国送到门国际物流</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2563eb',
                        secondary: '#f59e0b'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '2px',
                        DEFAULT: '4px',
                        'md': '8px',
                        'lg': '12px',
                        'xl': '16px',
                        '2xl': '20px',
                        '3xl': '24px',
                        'full': '9999px',
                        'button': '4px'
                    }
                }
            }
        }
    </script>
    <style>
        .font-['Pacifico'] {
            font-family: 'Pacifico', serif;
        }
        i {
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .rounded-button {
            border-radius: 4px;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 relative" style="width: 375px; min-height: 762px;">
    <!-- 顶部导航栏 -->
    <nav class="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="font-['Pacifico'] text-xl text-primary">logo</div>
            <div class="flex items-center space-x-2">
                <button class="text-sm px-2 py-1 rounded-button bg-gray-100 hover:bg-gray-200">中文</button>
                <button class="text-sm px-2 py-1 rounded-button bg-gray-100 hover:bg-gray-200">ไทย</button>
                <button class="text-sm px-2 py-1 rounded-button bg-gray-100 hover:bg-gray-200">EN</button>
            </div>
        </div>
        <div class="border-t border-gray-100">
            <div class="container mx-auto px-4 py-2 flex overflow-x-auto space-x-6">
                <a href="#" class="text-sm font-medium text-primary whitespace-nowrap">首页</a>
                <a href="#" class="text-sm font-medium text-gray-600 hover:text-primary whitespace-nowrap">服务介绍</a>
                <a href="#" class="text-sm font-medium text-gray-600 hover:text-primary whitespace-nowrap">关于我们</a>
                <a href="#" class="text-sm font-medium text-gray-600 hover:text-primary whitespace-nowrap">联系我们</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="pt-28 pb-20 px-4">
        <!-- 搜索查询区域 -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-6">
            <div class="flex space-x-1 mb-4 bg-gray-100 p-1 rounded-button">
                <button class="tab-btn flex-1 py-2 px-1 text-sm font-medium rounded-button active" data-tab="tracking">运单轨迹</button>
                <button class="tab-btn flex-1 py-2 px-1 text-sm font-medium rounded-button" data-tab="address">收货地址</button>
                <button class="tab-btn flex-1 py-2 px-1 text-sm font-medium rounded-button" data-tab="prohibited">禁运品名</button>
            </div>
            
            <div id="tracking" class="tab-content active">
                <div class="relative">
                    <input type="text" placeholder="请输入运单号" class="w-full py-3 px-4 border border-gray-300 rounded-button focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <button class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary text-white p-2 rounded-button">
                        <i class="fas fa-search text-white" style="width: 16px; height: 16px;"></i>
                    </button>
                </div>
            </div>
            
            <div id="address" class="tab-content">
                <div class="relative">
                    <input type="text" placeholder="请输入收货地址" class="w-full py-3 px-4 border border-gray-300 rounded-button focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <button class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary text-white p-2 rounded-button">
                        <i class="fas fa-search text-white" style="width: 16px; height: 16px;"></i>
                    </button>
                </div>
            </div>
            
            <div id="prohibited" class="tab-content">
                <div class="relative">
                    <input type="text" placeholder="请输入品名查询" class="w-full py-3 px-4 border border-gray-300 rounded-button focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <button class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary text-white p-2 rounded-button">
                        <i class="fas fa-search text-white" style="width: 16px; height: 16px;"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 快捷功能区 -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-6">
            <h3 class="text-lg font-semibold mb-4">快捷服务</h3>
            <div class="grid grid-cols-4 gap-4">
                <a href="#" class="flex flex-col items-center">
                    <div class="w-12 h-12 mb-2 rounded-full bg-blue-50 flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-primary" style="width: 24px; height: 24px;"></i>
                    </div>
                    <span class="text-xs text-center whitespace-nowrap overflow-hidden text-ellipsis">价格查询</span>
                </a>
                <a href="#" class="flex flex-col items-center">
                    <div class="w-12 h-12 mb-2 rounded-full bg-orange-50 flex items-center justify-center">
                        <i class="fas fa-headset text-secondary" style="width: 24px; height: 24px;"></i>
                    </div>
                    <span class="text-xs text-center whitespace-nowrap overflow-hidden text-ellipsis">在线客服</span>
                </a>
                <a href="#" class="flex flex-col items-center">
                    <div class="w-12 h-12 mb-2 rounded-full bg-green-50 flex items-center justify-center">
                        <i class="fas fa-map-marker-alt text-green-600" style="width: 24px; height: 24px;"></i>
                    </div>
                    <span class="text-xs text-center whitespace-nowrap overflow-hidden text-ellipsis">网点查询</span>
                </a>
                <a href="#" class="flex flex-col items-center">
                    <div class="w-12 h-12 mb-2 rounded-full bg-purple-50 flex items-center justify-center">
                        <i class="fas fa-calendar-check text-purple-600" style="width: 24px; height: 24px;"></i>
                    </div>
                    <span class="text-xs text-center whitespace-nowrap overflow-hidden text-ellipsis">预约寄件</span>
                </a>
            </div>
        </div>

        <!-- 服务介绍 -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-6">
            <h3 class="text-lg font-semibold mb-4">我们的服务</h3>
            <div class="relative rounded-xl overflow-hidden" style="height: 160px; background-image: url('https://ai-public.mastergo.com/ai/img_res/7b9be965ae31da2c4f19c4a4adc2419f.jpg'); background-size: cover; background-position: center;">
                <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                    <h4 class="text-white font-medium">泰国专线</h4>
                    <p class="text-white text-xs">3-5工作日送达，全程可追踪</p>
                </div>
            </div>
            <div class="grid grid-cols-2 gap-3 mt-3">
                <div class="bg-gray-50 rounded-lg p-3 flex items-center">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-plane text-blue-600" style="width: 18px; height: 18px;"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium">空运快递</h4>
                        <p class="text-xs text-gray-500">1-3工作日送达</p>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-3 flex items-center">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-ship text-green-600" style="width: 18px; height: 18px;"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium">海运服务</h4>
                        <p class="text-xs text-gray-500">7-15工作日送达</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 常见问题 -->
        <div class="bg-white rounded-xl shadow-sm p-4">
            <h3 class="text-lg font-semibold mb-4">常见问题</h3>
            <div class="space-y-3">
                <div class="border-b border-gray-100 pb-3">
                    <h4 class="text-sm font-medium mb-1">如何查询我的包裹状态？</h4>
                    <p class="text-xs text-gray-500">在运单轨迹查询中输入您的运单号，即可查看最新状态。</p>
                </div>
                <div class="border-b border-gray-100 pb-3">
                    <h4 class="text-sm font-medium mb-1">哪些物品不能寄往泰国？</h4>
                    <p class="text-xs text-gray-500">使用禁运品名查询功能，输入物品名称即可确认是否可寄送。</p>
                </div>
                <div class="border-b border-gray-100 pb-3">
                    <h4 class="text-sm font-medium mb-1">包裹丢失如何索赔？</h4>
                    <p class="text-xs text-gray-500">请联系我们的客服热线或在线客服处理。</p>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部标签栏 -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 z-50">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-4 gap-1 py-2">
                <a href="#" class="flex flex-col items-center py-2 text-primary">
                    <i class="fas fa-home" style="width: 20px; height: 20px;"></i>
                    <span class="text-xs mt-1">首页</span>
                </a>
                <a href="#" class="flex flex-col items-center py-2 text-gray-500">
                    <i class="fas fa-box-open" style="width: 20px; height: 20px;"></i>
                    <span class="text-xs mt-1">我的包裹</span>
                </a>
                <a href="#" class="flex flex-col items-center py-2 text-gray-500">
                    <i class="fas fa-user" style="width: 20px; height: 20px;"></i>
                    <span class="text-xs mt-1">个人中心</span>
                </a>
                <a href="#" class="flex flex-col items-center py-2 text-gray-500">
                    <i class="fas fa-ellipsis-h" style="width: 20px; height: 20px;"></i>
                    <span class="text-xs mt-1">更多</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- 浮动按钮 -->
    <div class="fixed bottom-24 right-4 z-50 space-y-2">
        <button class="w-12 h-12 bg-primary text-white rounded-full shadow-lg flex items-center justify-center">
            <i class="fas fa-arrow-up" style="width: 20px; height: 20px;"></i>
        </button>
        <button class="w-12 h-12 bg-secondary text-white rounded-full shadow-lg flex items-center justify-center">
            <i class="fas fa-headset" style="width: 20px; height: 20px;"></i>
        </button>
    </div>

    <script>
        // 选项卡切换功能
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                // 移除所有active类
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // 添加active类
                btn.classList.add('active');
                const tabId = btn.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // 返回顶部功能
        document.querySelector('.fa-arrow-up').closest('button').addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    </script>
</body>
</html>